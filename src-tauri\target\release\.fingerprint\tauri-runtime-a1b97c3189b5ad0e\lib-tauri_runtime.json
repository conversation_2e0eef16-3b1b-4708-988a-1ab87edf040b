{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 8361925469532012007, "deps": [[654232091421095663, "tauri_utils", false, 12011921763369382356], [3150220818285335163, "url", false, 3140987579113747913], [4143744114649553716, "raw_window_handle", false, 9730845076325814965], [7606335748176206944, "dpi", false, 1387235187864542575], [9010263965687315507, "http", false, 10156329732527234393], [9689903380558560274, "serde", false, 4567830511950114312], [10806645703491011684, "thiserror", false, 798670949607384445], [12943761728066819757, "build_script_build", false, 8007657132276830915], [14585479307175734061, "windows", false, 2741241964980341311], [16362055519698394275, "serde_json", false, 1560433525378758638], [16727543399706004146, "cookie", false, 14021352685865368555]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-a1b97c3189b5ad0e\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}