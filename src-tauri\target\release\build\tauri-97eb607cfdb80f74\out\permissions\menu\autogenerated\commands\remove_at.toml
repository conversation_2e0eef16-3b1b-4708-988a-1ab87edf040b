# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-remove-at"
description = "Enables the remove_at command without any pre-configured scope."
commands.allow = ["remove_at"]

[[permission]]
identifier = "deny-remove-at"
description = "Denies the remove_at command without any pre-configured scope."
commands.deny = ["remove_at"]
