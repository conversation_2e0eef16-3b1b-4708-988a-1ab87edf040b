# CORS 问题解决方案

## 问题描述

在浏览器环境中直接调用飞书API时，会遇到CORS（跨域资源共享）错误：

```
Access to fetch at 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal' 
from origin 'http://localhost:5173' has been blocked by CORS policy: 
Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 原因分析

### 什么是CORS？

CORS是浏览器的一种安全机制，用于限制网页从一个域向另一个域发起请求。当以下条件不同时，浏览器会认为是跨域请求：

- **协议**：http vs https
- **域名**：localhost vs open.feishu.cn  
- **端口**：5173 vs 443

### 为什么飞书API不支持CORS？

飞书的API服务器没有设置允许浏览器跨域访问的响应头：
- `Access-Control-Allow-Origin`
- `Access-Control-Allow-Methods`
- `Access-Control-Allow-Headers`

这是出于安全考虑，防止恶意网站直接调用API。

## 解决方案：Electron代理

### 方案概述

我们通过Electron主进程作为代理，绕过浏览器的CORS限制：

```
前端页面 → Electron主进程 → 飞书API服务器
   ↑              ↑              ↑
 渲染进程      Node.js环境      外部API
(有CORS限制)   (无CORS限制)    (不支持CORS)
```

### 技术实现

#### 1. Electron主进程代理

```typescript
// electron/main.ts
ipcMain.handle('feishu-api-request', async (_, options: {
  url: string,
  method: string,
  headers?: Record<string, string>,
  body?: string
}) => {
  return new Promise((resolve, reject) => {
    const url = new URL(options.url)
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname + url.search,
      method: options.method,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const req = https.request(requestOptions, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            data: jsonData
          })
        } catch (error) {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            statusText: res.statusMessage,
            data: data
          })
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    if (options.body) {
      req.write(options.body)
    }
    
    req.end()
  })
})
```

#### 2. Preload脚本暴露API

```typescript
// electron/preload.ts
contextBridge.exposeInMainWorld('electronAPI', {
  // ... 其他API
  feishuApiRequest: (options: {
    url: string,
    method: string,
    headers?: Record<string, string>,
    body?: string
  }) => ipcRenderer.invoke('feishu-api-request', options),
})
```

#### 3. 前端服务适配

```typescript
// src/services/feishuApi.ts
private async makeRequest(url: string, options: {
  method: string,
  headers?: Record<string, string>,
  body?: string
}): Promise<any> {
  // 检查是否在Electron环境中
  if (typeof window !== 'undefined' && window.electronAPI?.feishuApiRequest) {
    return await window.electronAPI.feishuApiRequest({
      url,
      method: options.method,
      headers: options.headers,
      body: options.body
    })
  } else {
    // 降级到普通fetch（开发环境）
    const response = await fetch(url, {
      method: options.method,
      headers: options.headers,
      body: options.body
    })
    
    const data = await response.json()
    
    return {
      ok: response.ok,
      status: response.status,
      statusText: response.statusText,
      data: data
    }
  }
}
```

## 优势

### 1. 安全性
- ✅ **绕过CORS限制**: 主进程不受浏览器CORS策略影响
- ✅ **保护敏感信息**: API密钥在主进程中处理，不暴露给渲染进程
- ✅ **控制访问**: 可以在主进程中添加额外的安全检查

### 2. 兼容性
- ✅ **Electron环境**: 使用代理进行API调用
- ✅ **开发环境**: 降级到普通fetch（可能有CORS问题，但便于调试）
- ✅ **类型安全**: 完整的TypeScript类型支持

### 3. 性能
- ✅ **直接连接**: 主进程直接连接API服务器，无额外代理服务器
- ✅ **连接复用**: Node.js的https模块支持连接复用
- ✅ **错误处理**: 完整的错误处理和重试机制

## 其他解决方案对比

### 方案1：代理服务器
```
前端 → 本地代理服务器 → 飞书API
```
❌ **缺点**: 需要额外的服务器，增加复杂性

### 方案2：浏览器插件
```
前端 → 浏览器插件 → 飞书API
```
❌ **缺点**: 需要用户安装插件，用户体验差

### 方案3：服务端API
```
前端 → 自建后端API → 飞书API
```
❌ **缺点**: 需要部署后端服务，增加维护成本

### 方案4：Electron代理（我们的方案）
```
前端 → Electron主进程 → 飞书API
```
✅ **优点**: 
- 无需额外服务器
- 完全本地化
- 安全可控
- 用户体验好

## 使用说明

### 开发环境
在开发环境中（`npm run dev`），应用运行在Electron中，会自动使用代理功能。

### 生产环境
打包后的应用（`npm run build`）会完全运行在Electron环境中，所有API调用都通过代理。

### 测试方法
1. 在设置页面配置飞书参数
2. 点击"测试连接"按钮
3. 如果配置正确，应该能成功连接

## 故障排除

### 问题1：仍然出现CORS错误
**原因**: 可能在浏览器中直接访问，而不是在Electron中
**解决**: 确保使用 `npm run dev` 启动Electron应用

### 问题2：连接超时
**原因**: 网络问题或API服务器不可达
**解决**: 检查网络连接和防火墙设置

### 问题3：认证失败
**原因**: App ID或App Secret错误
**解决**: 检查飞书开放平台的应用凭证

## 安全注意事项

1. **API密钥保护**: 确保App Secret不被泄露
2. **请求验证**: 在主进程中可以添加请求验证逻辑
3. **错误处理**: 不要在错误信息中暴露敏感信息
4. **日志记录**: 记录API调用日志，便于调试和审计
