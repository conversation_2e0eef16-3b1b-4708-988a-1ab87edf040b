import { app as n, <PERSON><PERSON><PERSON>Window as h, ipc<PERSON>ain as i, shell as g } from "electron";
import { fileURLToPath as E } from "node:url";
import t from "node:path";
import T from "node:https";
const u = t.dirname(E(import.meta.url)), x = t.join(n.getPath("userData"), "vs-re-data");
n.setPath("userData", x);
process.env.APP_ROOT = t.join(u, "..");
const c = process.env.VITE_DEV_SERVER_URL, v = t.join(process.env.APP_ROOT, "dist-electron"), f = t.join(process.env.APP_ROOT, "dist");
process.env.VITE_PUBLIC = c ? t.join(process.env.APP_ROOT, "public") : f;
let e;
function w() {
  e = new h({
    width: 1300,
    height: 900,
    resizable: !1,
    maximizable: !1,
    frame: !1,
    // 移除默认窗口栏
    titleBarStyle: "hidden",
    // 隐藏标题栏
    icon: t.join(process.env.VITE_PUBLIC, "app-icon.svg"),
    webPreferences: {
      preload: t.join(u, "preload.mjs"),
      contextIsolation: !0,
      nodeIntegration: !1
    }
  }), e.webContents.on("did-finish-load", () => {
    e == null || e.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  }), c ? e.loadURL(c) : e.loadFile(t.join(f, "index.html"));
}
n.on("window-all-closed", () => {
  process.platform !== "darwin" && (n.quit(), e = null);
});
n.on("activate", () => {
  h.getAllWindows().length === 0 && w();
});
i.handle("window-minimize", () => {
  e && e.minimize();
});
i.handle("window-maximize", () => {
  e && (e.isMaximized() ? e.unmaximize() : e.maximize());
});
i.handle("window-close", () => {
  e && e.close();
});
i.handle("open-external", (P, o) => {
  g.openExternal(o);
});
i.handle("feishu-api-request", async (P, o) => new Promise((m, _) => {
  const r = new URL(o.url), R = {
    hostname: r.hostname,
    port: r.port || 443,
    path: r.pathname + r.search,
    method: o.method,
    headers: {
      "Content-Type": "application/json",
      ...o.headers
    }
  }, l = T.request(R, (s) => {
    let d = "";
    s.on("data", (a) => {
      d += a;
    }), s.on("end", () => {
      const a = s.statusCode || 0;
      try {
        const p = JSON.parse(d);
        m({
          ok: a >= 200 && a < 300,
          status: a,
          statusText: s.statusMessage || "",
          data: p
        });
      } catch {
        m({
          ok: a >= 200 && a < 300,
          status: a,
          statusText: s.statusMessage || "",
          data: d
        });
      }
    });
  });
  l.on("error", (s) => {
    _(s);
  }), o.body && l.write(o.body), l.end();
}));
n.whenReady().then(() => {
  console.log("用户数据目录:", n.getPath("userData")), console.log("应用数据目录:", n.getPath("appData")), w();
});
export {
  v as MAIN_DIST,
  f as RENDERER_DIST,
  c as VITE_DEV_SERVER_URL
};
