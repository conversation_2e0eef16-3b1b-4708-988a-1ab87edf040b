{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 16219497806225609590, "deps": [[40386456601120721, "percent_encoding", false, 2557365499804753774], [41016358116313498, "hyper_util", false, 4706026316807979365], [784494742817713399, "tower_service", false, 9308697350011711417], [1906322745568073236, "pin_project_lite", false, 9610856809156372283], [2054153378684941554, "tower_http", false, 4399106988808053334], [2517136641825875337, "sync_wrapper", false, 7911722331742329275], [2883436298747778685, "rustls_pki_types", false, 16173985761374650601], [3150220818285335163, "url", false, 3140987579113747913], [5695049318159433696, "tower", false, 9778525692233992719], [5986029879202738730, "log", false, 14425135604263769896], [7620660491849607393, "futures_core", false, 6358293034038059262], [9010263965687315507, "http", false, 10156329732527234393], [9689903380558560274, "serde", false, 4567830511950114312], [10229185211513642314, "mime", false, 12583440156617515302], [11957360342995674422, "hyper", false, 13296465859356527807], [12186126227181294540, "tokio_native_tls", false, 15648200430072515255], [13077212702700853852, "base64", false, 14502108923394170693], [14084095096285906100, "http_body", false, 8313975913982668566], [14359893265615549706, "h2", false, 14776703630487608231], [14564311161534545801, "encoding_rs", false, 3423493201027954901], [16066129441945555748, "bytes", false, 7230401905503601804], [16362055519698394275, "serde_json", false, 1560433525378758638], [16542808166767769916, "serde_urlencoded", false, 10044670315879645830], [16785601910559813697, "native_tls_crate", false, 16349510919817028476], [16900715236047033623, "http_body_util", false, 4553408787421424042], [17531218394775549125, "tokio", false, 13860976087988092850], [18273243456331255970, "hyper_tls", false, 2613798631079788012]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-449d0a72ea4638d8\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}