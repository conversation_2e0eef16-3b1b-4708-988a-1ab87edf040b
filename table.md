# TDesign Vue Next Table API 文档 (完整版)

本文档包含了 `BaseTable`, `PrimaryTable`, `EnhancedTable` 的完整 `Props`、`Events`、`Methods` 以及所有相关的类型定义。

## **Table Components**

### **BaseTable Props**

| **参数**          | **说明**                                                     | **类型**                                                     | **默认值** |
| ----------------- | ------------------------------------------------------------ | ------------------------------------------------------------ | ---------- |
| bordered          | 是否显示表格边框                                             | `boolean`                                                    | `false`    |
| columns           | 列配置，泛型 T 指表格数据类型                                | `Array<BaseTableCol>`                                        | `[]`       |
| data              | 表格数据                                                     | `Array<T>`                                                   | `[]`       |
| disableDataSort   | 是否禁用数据排序功能。排序功能会影响 `data` 数据，如果 `data` 数据为受控，请自行对 `data` 进行排序 | `boolean`                                                    | `false`    |
| empty             | 空表格呈现的内容。值类型为 `string` 表示显示文案，值为 `function` 表示渲染函数 `h` | `string | TNode`                                             | '暂无数据' |
| firstFullRow      | 自定义显示第一行                                             | `string | TNode`                                             | -          |
| footData          | 表尾数据                                                     | `Array<T>`                                                   | `[]`       |
| footer            | 自定义表格尾部                                               | `string | TNode`                                             | -          |
| height            | 表格高度，超出后会出现滚动条。示例：`100`, `'100px'`, `'100%` | `string | number`                                            | -          |
| hover             | 是否显示悬浮效果                                             | `boolean`                                                    | `false`    |
| lastFullRow       | 自定义显示最后一行                                           | `string | TNode`                                             | -          |
| loading           | 是否显示加载中状态                                           | `boolean | TNode`                                            | `false`    |
| loadingProps      | 加载状态的配置                                               | `LoadingProps`                                               | -          |
| maxHeight         | 表格最大高度，超出后会出现滚动条。示例：`100`, `'100px'`, `'100%` | `string | number`                                            | -          |
| rowClassName      | 行类名，可以分别为 `tr` 添加不同类名。                       | `string | ((params: { row: T; rowIndex: number }) => string)` | -          |
| rowKey            | 指定 `row` 的 `key`                                          | `string`                                                     | `'id'`     |
| rowspanAndColspan | 合并单元格                                                   | `(params: RowspanAndColspanParams<T>) => { rowspan?: number; colspan?: number }` | -          |
| scroll            | 滚动条相关配置                                               | `Scroll`                                                     | -          |
| size              | 表格尺寸                                                     | `'small' | 'medium' | 'large'`                               | `'medium'` |
| stripe            | 是否显示斑马纹                                               | `boolean`                                                    | `false`    |
| tableContentWidth | 表格内容的总宽度，超出后会出现横向滚动条。如果 `columns` 中存在 `width`、`minWidth`、`maxWidth` 等属性，则可无需设置 | `string | number`                                            | -          |
| tableLayout       | 表格布局方式                                                 | `'auto' | 'fixed'`                                           | `'auto'`   |
| verticalAlign     | 单元格垂直对齐方式                                           | `'top' | 'middle' | 'bottom'`                                | `'middle'` |

### **BaseTable Events**

| **事件名**     | **说明**             | **参数**                                        |
| -------------- | -------------------- | ----------------------------------------------- |
| cell-click     | 单元格点击时触发     | `(context: CellContext<T>)`                     |
| page-change    | 分页发生变化时触发   | `(pageInfo: PageInfo, newDataSource: Array<T>)` |
| row-click      | 行点击时触发         | `(context: RowContext<T>)`                      |
| row-dblclick   | 行双击时触发         | `(context: RowContext<T>)`                      |
| row-hover      | 行 hover 时触发      | `(context: RowContext<T>)`                      |
| row-mousedown  | 行 mousedown 时触发  | `(context: RowContext<T>)`                      |
| row-mouseenter | 行 mouseenter 时触发 | `(context: RowContext<T>)`                      |
| row-mouseleave | 行 mouseleave 时触发 | `(context: RowContext<T>)`                      |
| row-mouseup    | 行 mouseup 时触发    | `(context: RowContext<T>)`                      |
| scroll         | 表格滚动时触发       | `(params: { e: WheelEvent })`                   |

### **PrimaryTable Props**

继承 `BaseTable` 的全部属性。

| **参数**                     | **说明**                                                     | **类型**                           | **默认值** |
| ---------------------------- | ------------------------------------------------------------ | ---------------------------------- | ---------- |
| asyncLoading                 | 异步加载状态。值为 `'loading'` 显示加载中，值为 `'load-more'` 显示加载更多 | `'' | 'loading' | 'load-more'`     | -          |
| columnController             | 列配置，泛型 T 指表格数据类型                                | `TableColumnController`            | -          |
| columns                      | 列配置                                                       | `Array<PrimaryTableCol<T>>`        | `[]`       |
| defaultExpandedRowKeys       | 默认展开的行                                                 | `Array<string | number>`           | `[]`       |
| expandedRow                  | 自定义展开行                                                 | `TNode<{ row: T; index: number }>` | -          |
| expandedRowKeys              | 展开的行，受控属性                                           | `Array<string | number>`           | `[]`       |
| expandIcon                   | 展开图标。`true` 显示默认图标，`false` 隐藏图标              | `boolean | TNode`                  | `true`     |
| expandOnRowClick             | 是否点击行任意位置都可展开/收起                              | `boolean`                          | `false`    |
| filterIcon                   | 过滤图标                                                     | `TNode`                            | -          |
| filterValue                  | 过滤，受控属性                                               | `FilterValue`                      | `null`     |
| hideSortTips                 | 是否隐藏排序提示                                             | `boolean`                          | `false`    |
| indeterminateSelectedRowKeys | 半选状态的行，受控属性                                       | `Array<string | number>`           | `[]`       |
| multipleSort                 | 是否支持多列排序                                             | `boolean`                          | `false`    |
| selectedRowKeys              | 选中行，受控属性                                             | `Array<string | number>`           | `[]`       |
| showMultipleSort             | 是否显示多级排序                                             | `boolean`                          | `false`    |
| sort                         | 排序，受控属性                                               | `TableSort`                        | -          |
| sortOnRowDraggable           | 行拖拽排序时，是否触发 `sort-change` 事件                    | `boolean`                          | `false`    |

### **PrimaryTable Events**

继承 `BaseTable` 的全部事件。

| **事件名**    | **说明**                                                     | **参数**                                                     |
| ------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| change        | 表格内容发生变化时触发，主要包括：排序、分页、过滤等         | `(data: TableChangeData, context: TableChangeContext<T>)`    |
| column-change | 列配置发生变化时触发（自由调整列宽、自由调整列顺序、列配置等） | `(data: ColumnChangeData)`                                   |
| data-change   | `data` 数据发生变化时触发                                    | `(data: Array<T>)`                                           |
| drag-sort     | 拖拽排序时触发                                               | `(context: DragSortContext<T>)`                              |
| expand-change | 展开行发生变化时触发                                         | `(expandedRowKeys: Array<string | number>, options: { expandedRowData: Array<T>; e: MouseEvent; row: T; rowIndex: number })` |
| filter-change | 筛选发生变化时触发                                           | `(filterValue: FilterValue, context: { col: PrimaryTableCol<T> })` |
| select-change | 选中行发生变化时触发                                         | `(selectedRowKeys: Array<string | number>, options: SelectChangeContext<T>)` |
| sort-change   | 排序发生变化时触发                                           | `(sort: TableSort, options: { currentDataSource: Array<T>; col: PrimaryTableCol<T> })` |

### **EnhancedTable Props**

继承 `PrimaryTable` 的全部属性。

| **参数**       | **说明**                                      | **类型**                              | **默认值** |
| -------------- | --------------------------------------------- | ------------------------------------- | ---------- |
| beforeDragSort | 拖拽排序前执行的方法，返回 `false` 可阻止拖拽 | `(params: { C: T; N: T }) => boolean` | -          |
| dragSort       | 拖拽排序                                      | `boolean`                             | `false`    |
| tree           | 树形结构相关配置                              | `TreeConfig`                          | -          |

### **EnhancedTable Events**

继承 `PrimaryTable` 的全部事件。

### **Table Methods**

| **方法名**        | **说明**     | **参数**                                   | **返回值**                                       |
| ----------------- | ------------ | ------------------------------------------ | ------------------------------------------------ |
| clearFilter       | 清空筛选     | `(colKey?: string)`                        | `void`                                           |
| resetData         | 重置 `data`  | `(data: Array<T>)`                         | `void`                                           |
| setFilter         | 设置筛选     | `(colKey: string, filterValue: any)`       | `void`                                           |
| setRowData        | 设置行数据   | `(rowKey: string | number, newRowData: T)` | `void`                                           |
| validateRowData   | 校验行数据   | `(rowKey: string | number)`                | `Promise<{ result: boolean; errorList: any[] }>` |
| validateTableData | 校验表格数据 | -                                          | `Promise<{ result: boolean; errorList: any[] }>` |

## **相关类型定义 (Type Definitions)**

### **BaseTableCol**

| **名称**  | **说明**         | **类型**                                         | **默认值** |
| --------- | ---------------- | ------------------------------------------------ | ---------- |
| align     | 对齐方式         | `'left' | 'right' | 'center'`                    | `'left'`   |
| attrs     | 自定义单元格属性 | `(context: CellContext<T>) => object`            | -          |
| cell      | 自定义单元格     | `string | TNode<CellData<T>>`                    | -          |
| children  | 用于多级表头     | `Array<BaseTableCol<T>>`                         | -          |
| className | 列类名           | `string | ((context: CellContext<T>) => string)` | -          |
| colKey    | 列唯一标识       | `string`                                         | -          |
| ellipsis  | 是否显示省略号   | `boolean | TNode<CellData<T>>`                   | `false`    |
| fixed     | 固定列           | `'left' | 'right'`                               | -          |
| minWidth  | 最小列宽         | `string | number`                                | -          |
| render    | 自定义列         | `TNode<RenderData<T>>`                           | -          |
| title     | 列标题           | `string | TNode`                                 | -          |
| width     | 列宽             | `string | number`                                | -          |

### **PrimaryTableCol**

继承 `BaseTableCol` 的全部属性。

| **名称** | **说明** | **类型**                             | **默认值** |
| -------- | -------- | ------------------------------------ | ---------- |
| filter   | 筛选配置 | `TableColumnFilter`                  | -          |
| sorter   | 排序函数 | `boolean | ((a: T, b: T) => number)` | `false`    |
| sortType | 排序方式 | `'asc' | 'desc' | 'all'`             | `'all'`    |

### **Scroll**

| **名称**         | **说明**           | **类型**               | **默认值** |
| ---------------- | ------------------ | ---------------------- | ---------- |
| bufferSize       | 虚拟滚动缓冲区大小 | `number`               | `10`       |
| isFixedRowHeight | 是否固定行高       | `boolean`              | `false`    |
| rowHeight        | 行高               | `number`               | -          |
| threshold        | 启动虚拟滚动的阈值 | `number`               | `100`      |
| type             | 滚动类型           | `'scroll' | 'virtual'` | `'scroll'` |

### **TreeConfig**

| **名称**            | **说明**                            | **类型** | **默认值**   |
| ------------------- | ----------------------------------- | -------- | ------------ |
| childrenKey         | 子节点字段名                        | `string` | `'children'` |
| indent              | 树形结构缩进                        | `number` | `24`         |
| treeNodeColumnIndex | 树形结构列所在的 `columns` 数组下标 | `number` | `0`          |

### **TableColumnController**

| **名称**       | **说明**                         | **类型**                           | **默认值** |
| -------------- | -------------------------------- | ---------------------------------- | ---------- |
| displayType    | 列配置按钮的展示方式             | `'fixed' | 'auto-hidden'`          | `'fixed'`  |
| fields         | 用于控制哪些列出现在列配置弹框中 | `Array<string>`                    | -          |
| onColumnChange | 列配置变化时触发                 | `(data: ColumnChangeData) => void` | -          |

### **TableSort**

```
{ sortBy: string; descending: boolean } | Array<{ sortBy: string; descending: boolean }>
```

### **FilterValue**

```
{ [colKey: string]: any }
```

### **TableChangeData**

```
{ sorter?: TableSort; filter?: FilterValue; pagination?: PageInfo; ... }
```

|      |      |      |      |
| ---- | ---- | ---- | ---- |
|      |      |      |      |
|      |      |      |      |
|      |      |      |      |
|      |      |      |      |
|      |      |      |      |
|      |      |      |      |