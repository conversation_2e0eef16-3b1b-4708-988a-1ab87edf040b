import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

// 接口定义
export interface JobApplication {
  id: number
  company: string
  applicationLink: string
  priority: number
  industry: string
  tags: string
  position: string
  location: string
  progress: string
  status: string
  progressDate: string
  applicationDate: string
  notes: string
  referralCode: string
}

// 本地存储键名
const STORAGE_KEY = 'job_applications_data'
const BACKUP_KEY = 'job_applications_backup'

// 数据验证函数
const validateJobApplication = (data: any): data is JobApplication => {
  return (
    typeof data === 'object' &&
    typeof data.id === 'number' &&
    typeof data.company === 'string' &&
    typeof data.applicationLink === 'string' &&
    typeof data.priority === 'number' &&
    typeof data.industry === 'string' &&
    typeof data.tags === 'string' &&
    typeof data.position === 'string' &&
    typeof data.location === 'string' &&
    typeof data.progress === 'string' &&
    typeof data.status === 'string' &&
    typeof data.progressDate === 'string' &&
    typeof data.applicationDate === 'string' &&
    typeof data.notes === 'string' &&
    typeof data.referralCode === 'string'
  )
}

export const useJobApplicationStore = defineStore('jobApplication', () => {
  // 状态
  const applications = ref<JobApplication[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const total = computed(() => applications.value.length)
  
  const paginatedApplications = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    return applications.value.slice(start, end)
  })

  // 从本地存储加载数据
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        // 验证数据格式
        if (Array.isArray(data) && data.every(validateJobApplication)) {
          applications.value = data
          console.log(`成功加载 ${data.length} 条求职记录`)
        } else {
          console.warn('存储的数据格式不正确，尝试从备份恢复')
          loadFromBackup()
        }
      } else {
        // 如果没有存储数据，初始化示例数据
        console.log('未找到存储数据，初始化默认数据')
        initializeDefaultData()
      }
    } catch (error) {
      console.error('加载数据失败:', error)
      loadFromBackup()
    }
  }

  // 从备份加载数据
  const loadFromBackup = () => {
    try {
      const backup = localStorage.getItem(BACKUP_KEY)
      if (backup) {
        const data = JSON.parse(backup)
        if (Array.isArray(data) && data.every(validateJobApplication)) {
          applications.value = data
          saveToStorage() // 恢复主数据
          console.log('从备份成功恢复数据')
          return
        }
      }
    } catch (error) {
      console.error('备份数据也损坏:', error)
    }
    // 如果备份也失败，初始化默认数据
    initializeDefaultData()
  }

  // 保存到本地存储（包含备份）
  const saveToStorage = () => {
    try {
      const jsonData = JSON.stringify(applications.value)

      // 创建备份
      const currentData = localStorage.getItem(STORAGE_KEY)
      if (currentData) {
        localStorage.setItem(BACKUP_KEY, currentData)
      }

      // 保存新数据
      localStorage.setItem(STORAGE_KEY, jsonData)

      console.log(`数据已保存，共 ${applications.value.length} 条记录`)
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 初始化默认数据
  const initializeDefaultData = () => {
    applications.value = [
      {
        id: 1,
        company: '腾讯',
        applicationLink: 'https://careers.tencent.com',
        priority: 1,
        industry: '互联网',
        tags: '大厂,稳定',
        position: '前端开发工程师',
        location: '深圳',
        progress: '一面',
        status: '等消息',
        progressDate: '2024-01-15',
        applicationDate: '2024-01-10',
        notes: '技术栈匹配度高',
        referralCode: 'TC2024001'
      },
      {
        id: 2,
        company: '阿里巴巴',
        applicationLink: 'https://job.alibaba.com',
        priority: 1,
        industry: '互联网',
        tags: '大厂,挑战',
        position: 'Vue.js开发工程师',
        location: '杭州',
        progress: '已投递',
        status: '等消息',
        progressDate: '2024-01-12',
        applicationDate: '2024-01-12',
        notes: '岗位要求高',
        referralCode: ''
      },
      {
        id: 3,
        company: '字节跳动',
        applicationLink: 'https://jobs.bytedance.com',
        priority: 2,
        industry: '互联网',
        tags: '创新,快节奏',
        position: '全栈开发工程师',
        location: '北京',
        progress: '笔试',
        status: '等待开始',
        progressDate: '2024-01-18',
        applicationDate: '2024-01-14',
        notes: '技术面试较难',
        referralCode: 'BD2024002'
      },
      {
        id: 4,
        company: '美团',
        applicationLink: 'https://zhaopin.meituan.com',
        priority: 2,
        industry: '互联网',
        tags: '生活服务,稳定',
        position: '前端工程师',
        location: '北京',
        progress: '二面',
        status: '已过',
        progressDate: '2024-01-20',
        applicationDate: '2024-01-08',
        notes: '业务导向强',
        referralCode: ''
      },
      {
        id: 5,
        company: '滴滴出行',
        applicationLink: 'https://careers.didiglobal.com',
        priority: 3,
        industry: '互联网',
        tags: '出行,技术',
        position: 'Web前端开发',
        location: '北京',
        progress: '已投递',
        status: '未过',
        progressDate: '2024-01-16',
        applicationDate: '2024-01-16',
        notes: '简历筛选未通过',
        referralCode: ''
      },
      // 添加更多测试数据以验证滚动功能
      ...Array.from({ length: 20 }, (_, index) => ({
        id: 6 + index,
        company: `测试公司${index + 1}`,
        applicationLink: `https://test${index + 1}.com`,
        priority: (index % 3) + 1,
        industry: ['互联网', '金融', '制造业', '教育', '医疗'][index % 5],
        tags: ['测试', '示例', '数据'][index % 3],
        position: `测试职位${index + 1}`,
        location: ['北京', '上海', '深圳', '杭州', '广州'][index % 5],
        progress: ['已投递', '笔试', '一面', '二面', '三面'][index % 5],
        status: ['等消息', '等我回复', '已过', '未过'][index % 4],
        progressDate: `2024-01-${String(10 + (index % 20)).padStart(2, '0')}`,
        applicationDate: `2024-01-${String(5 + (index % 25)).padStart(2, '0')}`,
        notes: `测试备注${index + 1}`,
        referralCode: index % 3 === 0 ? `TEST${String(index + 1).padStart(3, '0')}` : ''
      }))
    ]
    saveToStorage()
  }

  // 添加新记录
  const addApplication = (application: Omit<JobApplication, 'id'>) => {
    const newId = Math.max(...applications.value.map(item => item.id), 0) + 1

    // 获取当前日期作为默认投递时间
    const today = new Date().toISOString().split('T')[0]

    const newApplication: JobApplication = {
      ...application,
      id: newId,
      // 如果没有提供投递时间，则使用当前日期
      applicationDate: application.applicationDate || today
    }

    // 验证新记录
    if (validateJobApplication(newApplication)) {
      applications.value.unshift(newApplication)
      saveToStorage()
      console.log(`添加新记录: ${newApplication.company} - ${newApplication.position}`)
      return newApplication
    } else {
      console.error('新记录数据格式不正确')
      return null
    }
  }

  // 更新记录
  const updateApplication = (id: number, updates: Partial<JobApplication>) => {
    const index = applications.value.findIndex(app => app.id === id)

    if (index !== -1) {
      const originalApp = applications.value[index]
      const updatedApp = { ...originalApp, ...updates }

      // 验证更新后的记录
      if (validateJobApplication(updatedApp)) {
        applications.value[index] = updatedApp
        saveToStorage()
        console.log(`更新记录 ID:${id}`, Object.keys(updates))
        return applications.value[index]
      } else {
        console.error('更新后的记录数据格式不正确')
        return null
      }
    }
    console.warn(`未找到 ID 为 ${id} 的记录`)
    return null
  }

  // 删除记录
  const deleteApplication = (id: number) => {
    const index = applications.value.findIndex(app => app.id === id)
    if (index !== -1) {
      const deletedApp = applications.value[index]
      applications.value.splice(index, 1)
      saveToStorage()
      console.log(`删除记录: ${deletedApp.company} - ${deletedApp.position}`)
      return true
    }
    console.warn(`未找到 ID 为 ${id} 的记录`)
    return false
  }

  // 分页相关方法
  const setPage = (page: number) => {
    currentPage.value = page
  }

  const setPageSize = (size: number) => {
    pageSize.value = size
    currentPage.value = 1 // 重置到第一页
  }

  // 重置数据到初始状态
  const resetToDefault = () => {
    initializeDefaultData()
  }

  // 获取数据统计信息
  const getStatistics = () => {
    const stats = {
      total: applications.value.length,
      priority: { high: 0, medium: 0, low: 0 },
      progress: { signed: 0, interviewing: 0, applied: 0, rejected: 0 },
      status: { waiting: 0, passed: 0, failed: 0, abandoned: 0 }
    }

    applications.value.forEach(app => {
      // 优先级统计
      switch (app.priority) {
        case 1: stats.priority.high++; break
        case 2: stats.priority.medium++; break
        case 3: stats.priority.low++; break
      }

      // 进展统计
      if (app.progress === '签约') {
        stats.progress.signed++
      } else if (['一面', '二面', '三面', 'HR终面', '谈offer'].includes(app.progress)) {
        stats.progress.interviewing++
      } else if (app.progress === '已投递') {
        stats.progress.applied++
      }

      // 状态统计
      if (['等消息', '等我回复', '等待开始'].includes(app.status)) {
        stats.status.waiting++
      } else if (app.status === '已过') {
        stats.status.passed++
      } else if (['未过', '被调剂'].includes(app.status)) {
        stats.status.failed++
      } else if (['已放弃', '解约'].includes(app.status)) {
        stats.status.abandoned++
      }
    })

    return stats
  }

  // 监控数据变化，自动保存
  watch(
    applications,
    (newApplications) => {
      if (newApplications.length > 0) {
        // 延迟保存，避免频繁写入
        setTimeout(() => {
          saveToStorage()
        }, 100)
      }
    },
    { deep: true }
  )

  // 定期备份数据（每5分钟）
  if (typeof window !== 'undefined') {
    setInterval(() => {
      if (applications.value.length > 0) {
        const backupData = JSON.stringify(applications.value)
        localStorage.setItem(`${BACKUP_KEY}_${Date.now()}`, backupData)

        // 清理旧备份（保留最近3个）
        const keys = Object.keys(localStorage).filter(key => key.startsWith(`${BACKUP_KEY}_`))
        if (keys.length > 3) {
          keys.sort().slice(0, -3).forEach(key => {
            localStorage.removeItem(key)
          })
        }
      }
    }, 5 * 60 * 1000) // 5分钟
  }

  return {
    // 状态
    applications,
    loading,
    currentPage,
    pageSize,

    // 计算属性
    total,
    paginatedApplications,

    // 方法
    loadFromStorage,
    saveToStorage,
    addApplication,
    updateApplication,
    deleteApplication,
    setPage,
    setPageSize,
    resetToDefault,
    getStatistics
  }
})
