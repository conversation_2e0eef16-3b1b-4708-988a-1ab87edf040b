{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 14343488565670157652, "deps": [[376837177317575824, "build_script_build", false, 16923828517670788614], [4143744114649553716, "raw_window_handle", false, 9730845076325814965], [5986029879202738730, "log", false, 14425135604263769896], [10281541584571964250, "windows_sys", false, 3601598122992135184]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-3a3d10598d5b1c79\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}