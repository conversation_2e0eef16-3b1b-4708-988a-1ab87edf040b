// 求职申请相关类型定义

export interface JobApplication {
  id?: number
  company: string
  applicationLink: string
  priority: number
  industry: string
  tags: string
  position: string
  location: string
  progress: string
  status: string
  progressDate: string
  applicationDate: string
  notes: string
  referralCode: string
  createdAt?: Date
  updatedAt?: Date
}

export interface JobApplicationFilter {
  company?: string
  industry?: string
  position?: string
  location?: string
  progress?: string
  status?: string
  priority?: number
}

export interface JobApplicationSort {
  field: keyof JobApplication
  order: 'asc' | 'desc'
}
