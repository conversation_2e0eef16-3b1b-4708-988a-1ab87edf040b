# 默认飞书配置设置

## 配置信息

已为应用设置以下默认飞书配置：

### 基础配置
- **App Token**: `RR9cb8uqKaBUOGso9WTcBiXjnfh`
- **App ID**: `cli_a80ce54e0c78100c`
- **App Secret**: `6l0TOvHiepUuHD5VyxmRwcXHLoRalwfQ`

### 表格配置
- **热门秋招汇总 Table ID**: `tbl1arQX7A2jiLh4`
- **热门实习汇总 Table ID**: `tbl2tPFOPenoIQdg`
- **国企央企汇总 Table ID**: `tbl1UfSuMtFZ8GR9`

## 实现方式

### 1. 自动初始化
- 应用首次启动时会自动设置默认配置
- 如果用户已有配置，不会覆盖现有设置
- 配置同时保存到数据库和 localStorage

### 2. 配置加载优先级
1. **数据库配置**（最高优先级）
2. **localStorage 配置**（中等优先级）
3. **默认配置**（最低优先级，首次使用时）

### 3. 用户界面改进
- 输入框占位符显示默认值
- 新增"恢复默认配置"按钮
- 首次加载默认配置时会显示提示信息

## 使用说明

### 自动加载
- 新用户首次打开应用时，会自动加载默认配置
- 系统会显示提示："已加载默认飞书配置，您可以根据需要进行修改"

### 手动恢复
1. 打开设置页面
2. 在"飞书多维表格配置"区域
3. 点击"恢复默认配置"按钮
4. 确认操作后，所有配置将恢复为默认值

### 配置修改
- 用户可以随时修改任何配置项
- 点击"保存配置"后，新配置会覆盖默认值
- 修改后的配置会同时保存到数据库和本地存储

## 技术实现

### 代码位置
- **默认配置定义**: `src/services/feishuApi.ts`
- **配置加载逻辑**: `src/components/SettingsPage.vue`
- **数据库初始化**: `src/stores/databaseStore.ts`

### 关键方法
```typescript
// 默认配置
export const defaultFeishuConfig: FeishuConfig = {
  appToken: 'RR9cb8uqKaBUOGso9WTcBiXjnfh',
  tableId: 'tbl1arQX7A2jiLh4',
  internshipTableId: 'tbl2tPFOPenoIQdg',
  stateOwnedTableId: 'tbl1UfSuMtFZ8GR9',
  appId: 'cli_a80ce54e0c78100c',
  appSecret: '6l0TOvHiepUuHD5VyxmRwcXHLoRalwfQ'
}

// 恢复默认配置
const resetToDefault = async () => {
  feishuConfig.value = { ...defaultFeishuConfig }
  await databaseStore.saveFeishuConfig(defaultFeishuConfig)
  saveConfigToStorage(defaultFeishuConfig)
}
```

## 注意事项

1. **配置安全性**: App Secret 在界面中以密码形式显示
2. **配置验证**: 保存前会验证所有必填项是否完整
3. **错误处理**: 配置加载失败时会自动使用默认配置
4. **数据同步**: 配置会同时保存到数据库和 localStorage，确保数据一致性

## 用户体验

- **新用户**: 开箱即用，无需手动配置
- **现有用户**: 不影响已有配置，可选择性恢复默认值
- **配置管理**: 提供清晰的配置界面和操作提示
- **错误恢复**: 配置出错时可以快速恢复到默认状态

这样的设计确保了应用的易用性，同时保持了配置的灵活性。
