{"rustc": 1842507548689473721, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 15265839221216689736, "deps": [[1615478164327904835, "pin_utils", false, 897873084641257839], [1906322745568073236, "pin_project_lite", false, 9610856809156372283], [7620660491849607393, "futures_core", false, 6358293034038059262], [16240732885093539806, "futures_task", false, 15271411608545299425]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-28c01e41237b14e7\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}