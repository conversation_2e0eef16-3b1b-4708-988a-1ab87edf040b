<template>
  <div class="recruitment-table-container">
    <!-- 操作栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <t-button
          :theme="currentSummaryType === 'hot-autumn' ? 'warning' : 'default'"
          :variant="currentSummaryType === 'hot-autumn' ? 'base' : 'outline'"
          @click="showHotAutumnRecruitment"
        >
          🔥 热门秋招汇总
        </t-button>
        <t-button
          :theme="currentSummaryType === 'hot-internship' ? 'primary' : 'default'"
          :variant="currentSummaryType === 'hot-internship' ? 'base' : 'outline'"
          @click="showHotInternshipSummary"
        >
          💼 热门实习汇总
        </t-button>
        <t-button
          :theme="currentSummaryType === 'state-owned' ? 'success' : 'default'"
          :variant="currentSummaryType === 'state-owned' ? 'base' : 'outline'"
          @click="showStateOwnedEnterprises"
        >
          🏢 国企央企汇总
        </t-button>
      </div>
      <div class="toolbar-right">
        <!-- 筛选器 -->
        <div class="filters">

          <t-select
            v-model="filterValues.batch"
            :options="batchOptions"
            placeholder="筛选批次"
            clearable
            multiple
            size="small"
            style="width: 120px; margin-right: 8px;"
          />
          <t-select
            v-model="filterValues.isHot"
            :options="hotLevelOptions"
            placeholder="筛选热门"
            clearable
            multiple
            size="small"
            style="width: 100px; margin-right: 8px;"
          />
        </div>
        <div class="record-info">
          <span class="record-count">
            显示: {{ filteredAndSortedData.length }} / 总计: {{ getCurrentDataSource().length }} 条记录
          </span>
          <span v-if="isDataLoading" class="loading-indicator">
            <t-loading size="small" />
            数据加载中...
          </span>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-wrapper" ref="tableContainer" @scroll="onScroll">
      <t-table
        :data="filteredAndSortedData"
        :columns="columns"
        row-key="id"
        :loading="store.loading || isDataLoading || isLazyLoading"
        bordered
        stripe
        hover
        :resizable="true"
        :drag-sort="'col'"
        @filter-change="onFilterChangeDebounced"
        @drag-sort="onColumnDragSort"
      >
        <!-- 自定义空数据状态 -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div class="empty-title">
              {{ isDataLoading ? '数据加载中...' : '暂无招聘信息' }}
            </div>
            <div class="empty-description" v-if="!isDataLoading">
              {{ getEmptyDescription() }}
            </div>
          </div>
        </template>
        <!-- 更新列 -->
        <template #updateTime="{ row, rowIndex }">
          <t-input
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'updateTime'"
            v-model="editingValue"
            @blur="saveEdit(rowIndex, 'updateTime')"
            @keyup="(e) => handleKeyup(e, rowIndex, 'updateTime')"
            autofocus
          />
          <span v-else @click="startEdit(rowIndex, 'updateTime', row.updateTime)" class="editable-cell">
            {{ row.updateTime || '' }}
          </span>
        </template>

        <!-- 公司名称 -->
        <template #company="{ row, rowIndex }">
          <t-input
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'company'"
            v-model="editingValue"
            @blur="saveEdit(rowIndex, 'company')"
            @keyup="(e) => handleKeyup(e, rowIndex, 'company')"
            autofocus
          />
          <span v-else @click="startEdit(rowIndex, 'company', row.company)" class="editable-cell">
            {{ row.company || '' }}
          </span>
        </template>

        <!-- 投递链接 -->
        <template #applicationLink="{ row, rowIndex }">
          <t-input
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'applicationLink'"
            v-model="editingValue"
            @blur="saveEdit(rowIndex, 'applicationLink')"
            @keyup="(e) => handleKeyup(e, rowIndex, 'applicationLink')"
            autofocus
          />
          <span 
            v-else 
            @click="startEdit(rowIndex, 'applicationLink', row.applicationLink)" 
            @contextmenu.prevent="openLink(row.applicationLink)"
            class="editable-cell link-cell"
            :title="isValidUrl(row.applicationLink) ? '左键编辑，右键打开链接' : '点击编辑'"
          >
            {{ row.applicationLink || '' }}
          </span>
        </template>

        <!-- 行业 -->
        <template #industry="{ row, rowIndex }">
          <t-select
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'industry'"
            v-model="editingValue"
            @change="saveEdit(rowIndex, 'industry')"
            @blur="cancelEdit"
            :options="industryOptions"
            autofocus
            :popup-props="{ overlayClassName: 'custom-select-popup' }"
          />
          <span v-else @click="startEdit(rowIndex, 'industry', row.industry)" class="editable-cell">
            {{ row.industry || '' }}
          </span>
        </template>

        <!-- 标签 -->
        <template #tags="{ row, rowIndex }">
          <t-select
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'tags'"
            v-model="editingValue"
            @change="saveEdit(rowIndex, 'tags')"
            @blur="cancelEdit"
            :options="tagOptions"
            autofocus
            :popup-props="{ overlayClassName: 'custom-select-popup' }"
          />
          <span v-else @click="startEdit(rowIndex, 'tags', row.tags)" class="editable-cell">
            {{ row.tags || '' }}
          </span>
        </template>

        <!-- 批次 -->
        <template #batch="{ row, rowIndex }">
          <t-select
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'batch'"
            v-model="editingValue"
            @change="saveEdit(rowIndex, 'batch')"
            @blur="cancelEdit"
            :options="batchOptions"
            autofocus
            :popup-props="{ overlayClassName: 'custom-select-popup' }"
          />
          <span v-else @click="startEdit(rowIndex, 'batch', row.batch)" class="editable-cell">
            {{ row.batch || '' }}
          </span>
        </template>

        <!-- 热门 -->
        <template #isHot="{ row, rowIndex }">
          <t-select
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'isHot'"
            v-model="editingValue"
            :options="hotLevelOptions"
            @blur="saveEdit(rowIndex, 'isHot')"
            @change="saveEdit(rowIndex, 'isHot')"
            autofocus
            size="small"
          />
          <div v-else @click="startEdit(rowIndex, 'isHot', row.isHot)" class="hot-level-indicator editable-cell">
            <div class="hot-boxes">
              <div
                v-for="i in 3"
                :key="i"
                class="hot-box"
                :class="{
                  'hot-box-filled': i <= Number(row.isHot || 0),
                  'hot-box-level-1': i <= Number(row.isHot || 0) && Number(row.isHot || 0) === 1,
                  'hot-box-level-2': i <= Number(row.isHot || 0) && Number(row.isHot || 0) === 2,
                  'hot-box-level-3': i <= Number(row.isHot || 0) && Number(row.isHot || 0) === 3
                }"
              ></div>
            </div>
          </div>
        </template>

        <!-- 职位 -->
        <template #position="{ row, rowIndex }">
          <t-input
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'position'"
            v-model="editingValue"
            @blur="saveEdit(rowIndex, 'position')"
            @keyup="(e) => handleKeyup(e, rowIndex, 'position')"
            autofocus
          />
          <span v-else @click="startEdit(rowIndex, 'position', row.position)" class="editable-cell">
            {{ row.position || '' }}
          </span>
        </template>

        <!-- 地点 -->
        <template #location="{ row, rowIndex }">
          <t-input
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'location'"
            v-model="editingValue"
            @blur="saveEdit(rowIndex, 'location')"
            @keyup="(e) => handleKeyup(e, rowIndex, 'location')"
            autofocus
          />
          <span v-else @click="startEdit(rowIndex, 'location', row.location)" class="editable-cell">
            {{ row.location || '' }}
          </span>
        </template>

        <!-- 投递截至 -->
        <template #deadline="{ row, rowIndex }">
          <t-date-picker
            v-if="editingCell.rowIndex === rowIndex && editingCell.column === 'deadline'"
            v-model="editingValue"
            @pick="(value) => handleDateChangeWithParams(rowIndex, 'deadline', value)"
            format="YYYY-MM-DD"
            autofocus
          />
          <span v-else @click="startEdit(rowIndex, 'deadline', row.deadline)" class="editable-cell">
            {{ row.deadline || '' }}
          </span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ rowIndex }">
          <t-button
            theme="primary"
            variant="outline"
            size="small"
            @click="addToJobApplication(rowIndex)"
            title="添加到求职申请跟踪表"
            class="add-to-job-btn"
          >
            <add-icon />
          </t-button>
        </template>
      </t-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import {
  Table as TTable,
  Button as TButton,
  Input as TInput,
  Select as TSelect,
  DatePicker as TDatePicker,
  Loading as TLoading,
  MessagePlugin
} from 'tdesign-vue-next'
import { AddIcon } from 'tdesign-icons-vue-next'
import { useRecruitmentStore } from '../stores/recruitmentStore'
import { useJobApplicationStore } from '../stores/jobApplicationStore'
import { useDatabaseStore } from '../stores/databaseStore'
import { industryOptions, batchOptions, tagOptions } from '../types/recruitment'

// 热门级别选项
const hotLevelOptions = [
  { label: '低', value: '1' },
  { label: '中', value: '2' },
  { label: '高', value: '3' }
]

// 这些函数暂时保留，可能在未来的功能中使用
// const getHotLevelLabel = (value: string | number) => {
//   const strValue = String(value)
//   switch (strValue) {
//     case '1': return '低'
//     case '2': return '中'
//     case '3': return '高'
//     default: return ''
//   }
// }

// const getHotLevelColor = (value: string | number) => {
//   const strValue = String(value)
//   switch (strValue) {
//     case '1': return '#52c41a' // 绿色
//     case '2': return '#faad14' // 黄色
//     case '3': return '#ff4d4f' // 红色
//     default: return '#d9d9d9' // 灰色
//   }
// }

// Store
const store = useRecruitmentStore()
const jobApplicationStore = useJobApplicationStore()
const databaseStore = useDatabaseStore()

// 筛选和排序状态
const filterValues = ref<Record<string, any>>({})
const currentSort = ref<{ sortBy?: string; descending?: boolean }>({})

// 当前选中的汇总类型
const currentSummaryType = ref('hot-autumn')

// 性能优化：缓存计算结果（暂时注释，未来可能使用）
// const cachedFilteredData = ref<Record<string, any[]>>({
//   'hot-autumn': [],
//   'hot-internship': [],
//   'state-owned': []
// })

// 加载状态
const isDataLoading = ref(false)

// 防抖计时器
let filterDebounceTimer: NodeJS.Timeout | null = null

// 懒加载相关状态
const isLazyLoading = ref(false) // 是否正在懒加载
const displayedData = ref<any[]>([])
const batchSize = 50 // 每批加载50条数据
const currentBatch = ref(0)
const isLazyMode = ref(true) // 是否启用懒加载模式
const allProcessedData = ref<any[]>([]) // 缓存所有处理后的数据

// 表格容器引用
const tableContainer = ref<HTMLElement>()



// 所有数据源都使用数据库存储（databaseStore）
// 热门秋招、热门实习、国企央企数据统一使用数据库管理

// 获取当前数据源
const getCurrentDataSource = () => {
  switch (currentSummaryType.value) {
    case 'hot-autumn':
      return databaseStore.hotAutumnData // 热门秋招使用数据库store
    case 'hot-internship':
      return databaseStore.hotInternshipData // 热门实习使用数据库store
    case 'state-owned':
      return databaseStore.stateOwnedData // 国企央企使用数据库store
    default:
      return databaseStore.hotAutumnData
  }
}

// 高性能的数据筛选和排序函数
const processData = (sourceData: any[], filters: Record<string, any>, sort: { sortBy?: string; descending?: boolean }) => {
  // 如果没有筛选和排序，直接返回原数据引用
  const hasFilters = Object.keys(filters).some(key => {
    const value = filters[key]
    return value && (Array.isArray(value) ? value.length > 0 : true)
  })

  if (!hasFilters && !sort.sortBy) {
    return sourceData
  }

  let data = sourceData

  // 应用筛选（只在需要时创建新数组）
  if (hasFilters) {
    data = sourceData.filter(item => {
      for (const key in filters) {
        const filterValue = filters[key]
        if (!filterValue || (Array.isArray(filterValue) && filterValue.length === 0)) {
          continue
        }

        const cellValue = item[key]
        if (Array.isArray(filterValue)) {
          if (!filterValue.includes(cellValue)) return false
        } else if (cellValue !== filterValue) {
          return false
        }
      }
      return true
    })
  }

  // 应用排序（只在需要时排序）
  if (sort.sortBy) {
    const { sortBy, descending } = sort
    data = [...data].sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]

      if (sortBy === 'deadline') {
        // 优化日期排序
        const aTime = aValue ? new Date(aValue).getTime() : 0
        const bTime = bValue ? new Date(bValue).getTime() : 0
        return descending ? bTime - aTime : aTime - bTime
      }

      return 0
    })
  }

  return data
}

// 懒加载数据的核心逻辑
const loadDataBatch = (batchIndex: number, allData: any[]) => {
  const start = batchIndex * batchSize
  const end = Math.min(start + batchSize, allData.length)
  return allData.slice(start, end)
}

// 初始化懒加载
const initLazyLoading = (allData: any[]) => {
  // 缓存所有处理后的数据
  allProcessedData.value = allData

  if (!isLazyMode.value || allData.length <= batchSize) {
    // 数据量小或禁用懒加载模式时，直接显示所有数据
    displayedData.value = allData
    return
  }

  // 重置状态
  displayedData.value = []
  currentBatch.value = 0

  // 加载第一批数据
  const firstBatch = loadDataBatch(0, allData)
  displayedData.value = firstBatch
  currentBatch.value = 1
}

// 懒加载更多数据
const loadMoreData = () => {
  if (isLazyLoading.value || !isLazyMode.value) return

  const allData = allProcessedData.value

  // 检查是否还有更多数据
  if (displayedData.value.length >= allData.length) return

  isLazyLoading.value = true

  // 使用 requestAnimationFrame 确保不阻塞UI
  requestAnimationFrame(() => {
    // 加载下一批数据
    const nextBatch = loadDataBatch(currentBatch.value, allData)
    displayedData.value.push(...nextBatch)
    currentBatch.value++

    isLazyLoading.value = false
  })
}

// 滚动监听，实现懒加载
const onScroll = (event: Event) => {
  if (!isLazyMode.value || isLazyLoading.value) return

  const target = event.target as HTMLElement
  const { scrollTop, scrollHeight, clientHeight } = target

  // 当滚动到距离底部100px时触发懒加载
  const threshold = 100
  if (scrollTop + clientHeight >= scrollHeight - threshold) {
    loadMoreData()
  }
}

// 计算过滤和排序后的数据
const filteredAndSortedData = computed(() => {
  const sourceData = getCurrentDataSource()

  // 如果数据源为空，直接返回空数组
  if (!sourceData || sourceData.length === 0) {
    return []
  }

  const processedData = processData(sourceData, filterValues.value, currentSort.value)

  // 如果启用懒加载模式，返回已显示的数据
  if (isLazyMode.value && processedData.length > batchSize) {
    return displayedData.value
  }

  // 否则返回所有处理后的数据
  return processedData
})

// 监听数据变化，重新初始化懒加载
watch([() => getCurrentDataSource(), filterValues, currentSort], () => {
  if (!isLazyMode.value) return

  const allData = processData(getCurrentDataSource(), filterValues.value, currentSort.value)
  initLazyLoading(allData)
}, { deep: true })



// 表格列配置
const columns = ref([
  {
    colKey: 'updateTime',
    title: '更新',
    width: 70,
    cell: 'updateTime',
    ellipsis: true
  },
  {
    colKey: 'company',
    title: '公司',
    width: 110,
    cell: 'company',
    ellipsis: true
  },
  {
    colKey: 'applicationLink',
    title: '投递链接',
    width: 80,
    cell: 'applicationLink',
    ellipsis: true
  },
  {
    colKey: 'industry',
    title: '行业',
    width: 80,
    cell: 'industry',
    ellipsis: true
  },
  {
    colKey: 'tags',
    title: '标签',
    width: 80,
    cell: 'tags',
    ellipsis: true
  },
  {
    colKey: 'batch',
    title: '批次',
    width: 80,
    cell: 'batch',
    ellipsis: true
  },
  {
    colKey: 'isHot',
    title: '热门',
    width: 80,
    cell: 'isHot',
    align: 'center' as const
  },
  {
    colKey: 'position',
    title: '职位',
    width: 110,
    cell: 'position',
    ellipsis: true
  },
  {
    colKey: 'location',
    title: '地点',
    width: 80,
    cell: 'location',
    ellipsis: true
  },
  {
    colKey: 'deadline',
    title: '投递截至',
    width: 110,
    cell: 'deadline'
  },
  {
    colKey: 'actions',
    title: '操作',
    width: 70,
    cell: 'actions',
    fixed: 'right' as const
  }
])

// 编辑状态
const editingCell = reactive({
  rowIndex: -1,
  column: ''
})
const editingValue = ref('')

// 优化的初始化逻辑
const initializeDataSources = async () => {
  isDataLoading.value = true

  try {
    // 确保数据库已初始化
    if (!databaseStore.initialized) {
      await databaseStore.initializeDatabase()
    }

    // 同步热门秋招数据到数据库（如果原有store有数据且数据库为空）
    if (store.recruitments.length > 0 && databaseStore.hotAutumnData.length === 0) {
      await databaseStore.saveHotAutumnData([...store.recruitments])
    }

    // 如果数据库中没有热门秋招数据，但原有store为空，则从localStorage加载
    if (databaseStore.hotAutumnData.length === 0 && store.recruitments.length === 0) {
      store.loadFromStorage()
      if (store.recruitments.length > 0) {
        await databaseStore.saveHotAutumnData([...store.recruitments])
      }
    }
  } catch (error) {
    MessagePlugin.error('数据加载失败，请刷新页面重试')
  } finally {
    isDataLoading.value = false
  }
}

// 监听数据源变化，清理防抖计时器
watch(currentSummaryType, () => {
  if (filterDebounceTimer) {
    clearTimeout(filterDebounceTimer)
    filterDebounceTimer = null
  }
})

// 初始化
onMounted(async () => {
  // 异步初始化数据源，包括数据迁移
  await initializeDataSources()

  // 初始化懒加载
  if (isLazyMode.value) {
    const allData = processData(getCurrentDataSource(), filterValues.value, currentSort.value)
    initLazyLoading(allData)
  }
})

// 防抖的筛选处理 - 优化响应时间
const onFilterChangeDebounced = (filterInfo: any) => {
  if (filterDebounceTimer) {
    clearTimeout(filterDebounceTimer)
  }

  filterDebounceTimer = setTimeout(() => {
    filterValues.value = { ...filterInfo }
  }, 150) // 减少到150ms，提升响应性
}

// 筛选变化处理（保留原方法供其他地方使用）
// const onFilterChange = (filterInfo: any) => {
//   filterValues.value = { ...filterInfo }
// }

// 列拖拽排序处理
const onColumnDragSort = (params: any) => {
  if (params && params.newData) {
    columns.value = params.newData
  }
}

// 编辑相关方法
const startEdit = (rowIndex: number, column: string, value: any) => {
  editingCell.rowIndex = rowIndex
  editingCell.column = column
  // 确保editingValue始终是字符串
  if (typeof value === 'boolean') {
    editingValue.value = value ? '热门' : ''
  } else {
    editingValue.value = String(value || '')
  }
}

const cancelEdit = () => {
  editingCell.rowIndex = -1
  editingCell.column = ''
  editingValue.value = ''
}

const saveEdit = async (rowIndex: number, column: string) => {
  if (editingCell.rowIndex === rowIndex && editingCell.column === column) {
    const recruitment = filteredAndSortedData.value[rowIndex]
    if (recruitment) {
      // 更新当前数据源中的数据
      ;(recruitment as any)[column] = editingValue.value

      // 根据当前数据源类型保存数据
      if (currentSummaryType.value === 'hot-autumn') {
        // 热门秋招数据保存到recruitmentStore
        store.updateRecruitment(recruitment.id, {
          [column]: editingValue.value
        })
      } else {
        // 实习和国企数据保存到数据库
        try {
          if (currentSummaryType.value === 'hot-internship') {
            await databaseStore.updateHotInternshipRecord(recruitment.id, {
              [column]: editingValue.value
            })
          } else if (currentSummaryType.value === 'state-owned') {
            await databaseStore.updateStateOwnedRecord(recruitment.id, {
              [column]: editingValue.value
            })
          }
        } catch (error) {
          MessagePlugin.error('保存失败')
          return
        }
      }

      cancelEdit()
      MessagePlugin.success('保存成功')
    }
  }
}

// 处理键盘事件
const handleKeyup = (event: any, rowIndex: number, column: string) => {
  if (event && event.key === 'Enter') {
    saveEdit(rowIndex, column)
  } else if (event && event.key === 'Escape') {
    cancelEdit()
  }
}

// 防止重复调用的标志
let isProcessingDateChange = false

// 处理日期选择器的变化
const handleDateChangeWithParams = (rowIndex: number, column: string, value: any) => {
  // 防止重复调用
  if (!value || isProcessingDateChange) {
    return
  }
  
  // 设置处理标志
  isProcessingDateChange = true
  
  const recruitment = filteredAndSortedData.value[rowIndex]
  
  if (recruitment) {
    // 转换日期值为字符串格式，修复时区问题
    let dateString = ''
    if (value) {
      if (typeof value === 'string') {
        // 如果已经是字符串格式，直接使用
        dateString = value
      } else if (value instanceof Date) {
        // 修复时区问题：使用本地时间而不是UTC时间
        const year = value.getFullYear()
        const month = String(value.getMonth() + 1).padStart(2, '0')
        const day = String(value.getDate()).padStart(2, '0')
        dateString = `${year}-${month}-${day}`
      } else if (value && value.toString) {
        dateString = value.toString()
      } else {
        dateString = String(value)
      }
    }
    
    const result = store.updateRecruitment(recruitment.id, {
      [column]: dateString
    })
    
    if (result) {
      cancelEdit()
      MessagePlugin.success('日期保存成功')
    } else {
      MessagePlugin.error('保存失败')
    }
  }
  
  // 延迟重置处理标志，防止快速重复调用
  setTimeout(() => {
    isProcessingDateChange = false
  }, 100)
}

// URL验证
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 打开链接
const openLink = (url: string) => {
  if (isValidUrl(url)) {
    window.electronAPI?.openExternal(url)
  }
}

// 优化的切换逻辑 - 减少UI阻塞
const switchDataSource = async (newType: string) => {
  if (currentSummaryType.value === newType) {
    return // 如果已经是当前类型，不需要切换
  }

  // 立即切换，不显示加载状态（避免UI闪烁）
  currentSummaryType.value = newType

  // 清空筛选条件（在下一个tick中执行，避免阻塞）
  nextTick(() => {
    filterValues.value = {}
    currentSort.value = {}
  })

  // 异步显示成功消息
  setTimeout(() => {
    const dataLength = getCurrentDataSource().length
    MessagePlugin.success(`切换成功 - 共 ${dataLength} 条记录`)
  }, 100)
}

// 工具栏按钮方法
const showHotAutumnRecruitment = () => {
  switchDataSource('hot-autumn')
}

const showHotInternshipSummary = () => {
  switchDataSource('hot-internship')
}

const showStateOwnedEnterprises = () => {
  switchDataSource('state-owned')
}





// 获取空状态描述
const getEmptyDescription = () => {
  const typeDescriptions = {
    'hot-autumn': '当前没有热门秋招信息，请在设置页面导入数据',
    'hot-internship': '当前没有热门实习信息，请在设置页面导入数据',
    'state-owned': '当前没有国企央企信息，请在设置页面导入数据'
  }
  return typeDescriptions[currentSummaryType.value as keyof typeof typeDescriptions] || '暂无数据'
}



// 添加到求职申请跟踪表
const addToJobApplication = (rowIndex: number) => {
  const recruitment = filteredAndSortedData.value[rowIndex]
  if (recruitment) {
    // 获取当前日期作为投递时间
    const today = new Date().toISOString().split('T')[0]

    // 只添加指定的字段，其他默认留空
    const jobApplication = {
      company: recruitment.company || '',           // 公司对应公司
      applicationLink: recruitment.applicationLink || '', // 投递链接对应投递链接
      priority: 2,                                 // 默认中等重视度
      industry: '',                                // 默认留空
      tags: '',                                    // 默认留空（必需字段）
      position: recruitment.position || '',        // 职位对应职位
      location: recruitment.location || '',        // 地点对应地点
      progress: '已投递',                          // 默认进展为已投递
      status: '等消息',                            // 默认状态为等消息
      progressDate: '',                            // 默认留空
      applicationDate: today,                      // 投递时间为当前日期
      notes: '',                                   // 默认留空
      referralCode: ''                             // 默认留空
    }

    const result = jobApplicationStore.addApplication(jobApplication)
    if (result) {
      MessagePlugin.success(`已添加到求职申请跟踪表: ${recruitment.company} - ${recruitment.position}`)
    } else {
      MessagePlugin.error('添加失败')
    }
  }
}



// 更新时间现在直接从数据中显示，不需要格式化函数
</script>

<style scoped>
.recruitment-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #fff;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.record-count {
  font-size: 14px;
  color: #666;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #1890ff;
  font-weight: 500;
}

.table-wrapper {
  height: 800px;
  width: 100%;
  overflow-y: auto;
  overflow-x: auto;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  /* 为表格容器添加自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 自定义滚动条样式 - 垂直和水平滚动条 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.table-wrapper::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* 可编辑单元格样式 */
.editable-cell {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  display: block;
  min-height: 20px;
}

.editable-cell:hover {
  background-color: #f0f0f0;
}

.link-cell {
  color: #1890ff;
  text-decoration: underline;
}

.link-cell:hover {
  background-color: #e6f4ff;
}

/* 移除表格组件内部的滚动条，使用外层容器的滚动条 */
:deep(.t-table-container) {
  overflow: visible !important;
  border: none !important;
}

:deep(.t-table__content) {
  overflow: visible !important;
}

:deep(.t-table__body-container) {
  overflow: visible !important;
}

:deep(.t-table__header-container) {
  overflow: visible !important;
}

/* 移除表格组件内部的滚动条，使用外层容器的滚动条 */
:deep(.t-table-container) {
  overflow: visible !important;
  border: none !important;
}

:deep(.t-table__content) {
  overflow: visible !important;
}

:deep(.t-table__body-container) {
  overflow: visible !important;
}

:deep(.t-table__header-container) {
  overflow: visible !important;
}

/* 确保表格适应容器 */
:deep(.t-table) {
  height: auto !important;
  max-height: none !important;
  width: 100%;
  min-width: 1000px;
  table-layout: fixed;
  font-size: 12px;
  border: none;
}

/* 确保所有行都能正常渲染 */
:deep(.t-table__body .t-table__row) {
  display: table-row !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 禁用任何虚拟滚动相关功能 */
:deep(.t-table__virtual-scroll) {
  display: none !important;
}

:deep(.t-table__virtual-scroll-container) {
  display: none !important;
}

/* 自定义选择器弹出层样式 */
:deep(.custom-select-popup) {
  font-size: 12px;
}

/* 隐藏TDesign内部组件的size属性警告 */
:deep(.t-select-panel) {
  font-size: inherit;
}

/* 更新时间列样式 */
.update-time {
  font-size: 12px;
  color: #999;
}

/* 添加到求职申请按钮样式 */
.add-to-job-btn {
  border-radius: 4px;
  padding: 4px 8px !important;
  min-width: 32px !important;
  height: 28px !important;
}

.add-to-job-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

/* 工具栏按钮样式 */
.toolbar-left .t-button {
  margin-right: 8px;
  transition: all 0.2s ease;
}

.toolbar-left .t-button:hover {
  transform: translateY(-1px);
}

.add-to-job-btn:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 移除所有按钮的焦点边框 */
:deep(.t-button) {
  outline: none !important;
}

:deep(.t-button:focus) {
  outline: none !important;
  box-shadow: none !important;
}

/* 筛选器样式 */
.filters {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

/* 热门级别指示器样式 */
.hot-level-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px;
}

.hot-boxes {
  display: flex;
  gap: 2px;
}

.hot-box {
  width: 12px;
  height: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #f5f5f5;
  transition: all 0.2s ease;
}

.hot-box-filled {
  border-color: transparent;
}

.hot-box-level-1 {
  background-color: #52c41a; /* 绿色 */
}

.hot-box-level-2 {
  background-color: #faad14; /* 黄色 */
}

.hot-box-level-3 {
  background-color: #ff4d4f; /* 红色 */
}

.hot-level-indicator:hover .hot-box {
  transform: scale(1.1);
}

/* 表格字体大小和样式 */
:deep(.t-table__header) {
  font-size: 12px;
}

:deep(.t-table__body) {
  font-size: 12px;
}

:deep(.t-input) {
  font-size: 12px;
}

:deep(.t-select) {
  font-size: 12px;
}

:deep(.t-date-picker) {
  font-size: 12px;
}

/* 表格单元格内容处理 */
:deep(.t-table__cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 8px 12px;
}

/* 移除分页组件的滚动条 */
:deep(.t-pagination) {
  overflow: visible !important;
}

/* 悬浮效果增强 */
:deep(.t-table__row:hover) {
  background-color: #f5f5f5;
  transition: background-color 0.2s ease;
}

/* 空数据状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  margin: 20px;
  border: 2px dashed #e2e8f0;
  transition: all 0.3s ease;
}

.empty-state:hover {
  border-color: #cbd5e1;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.empty-icon {
  margin-bottom: 24px;
  color: #64748b;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.empty-icon svg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 12px;
  letter-spacing: -0.025em;
}

.empty-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  max-width: 400px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .empty-state {
    padding: 60px 20px;
    margin: 10px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
    max-width: 300px;
  }
}
</style>
