<template>
  <div v-if="showError" class="database-error-overlay">
    <div class="error-dialog">
      <div class="error-icon">⚠️</div>
      <h3>数据库连接异常</h3>
      <p>{{ errorMessage }}</p>
      <div class="error-actions">
        <t-button theme="primary" @click="retryConnection" :loading="retrying">
          重新连接
        </t-button>
        <t-button theme="default" @click="dismissError">
          忽略
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useDatabaseStore } from '../stores/databaseStore'
import { MessagePlugin } from 'tdesign-vue-next'

const databaseStore = useDatabaseStore()

const showError = ref(false)
const errorMessage = ref('')
const retrying = ref(false)

// 监听数据库错误
const handleDatabaseError = (error: any) => {
  console.error('DatabaseErrorHandler: 捕获到数据库错误:', error)
  
  if (error.name === 'DatabaseClosedError' || error.message?.includes('DatabaseClosedError')) {
    errorMessage.value = '数据库连接已断开，这可能会影响数据的保存和读取。请尝试重新连接。'
    showError.value = true
  } else {
    errorMessage.value = '数据库操作出现异常，请检查应用状态。'
    showError.value = true
  }
}

// 重试连接
const retryConnection = async () => {
  retrying.value = true
  try {
    const success = await databaseStore.reinitializeDatabase()
    if (success) {
      MessagePlugin.success('数据库重新连接成功')
      showError.value = false
    } else {
      MessagePlugin.error('数据库重新连接失败，请重启应用')
    }
  } catch (error) {
    console.error('重新连接失败:', error)
    MessagePlugin.error('数据库重新连接失败，请重启应用')
  } finally {
    retrying.value = false
  }
}

// 忽略错误
const dismissError = () => {
  showError.value = false
}

// 全局错误监听
let errorHandler: ((event: ErrorEvent) => void) | null = null

onMounted(() => {
  // 监听全局错误
  errorHandler = (event: ErrorEvent) => {
    if (event.error && (
      event.error.name === 'DatabaseClosedError' || 
      event.message?.includes('DatabaseClosedError')
    )) {
      handleDatabaseError(event.error)
    }
  }
  
  window.addEventListener('error', errorHandler)
  
  // 监听未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && (
      event.reason.name === 'DatabaseClosedError' || 
      event.reason.message?.includes('DatabaseClosedError')
    )) {
      handleDatabaseError(event.reason)
    }
  })
})

onUnmounted(() => {
  if (errorHandler) {
    window.removeEventListener('error', errorHandler)
  }
})
</script>

<style scoped>
.database-error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.error-dialog {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-dialog h3 {
  margin: 0 0 12px 0;
  color: #e34d59;
  font-size: 18px;
}

.error-dialog p {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.error-actions .t-button {
  min-width: 80px;
}
</style>
