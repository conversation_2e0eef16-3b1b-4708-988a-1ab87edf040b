import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { RecruitmentSummary } from '../types/recruitment'

export const useTableDataStore = defineStore('tableData', () => {
  // 三个表格的数据
  const hotAutumnData = ref<RecruitmentSummary[]>([])
  const hotInternshipData = ref<RecruitmentSummary[]>([])
  const stateOwnedData = ref<RecruitmentSummary[]>([])

  // 保存到本地存储
  const saveToStorage = () => {
    try {
      localStorage.setItem('hot-internship-data', JSON.stringify(hotInternshipData.value))
      localStorage.setItem('state-owned-data', JSON.stringify(stateOwnedData.value))
      console.log('Store: 表格数据已保存到本地存储')
      console.log('Store: 实习数据条数:', hotInternshipData.value.length)
      console.log('Store: 国企数据条数:', stateOwnedData.value.length)
    } catch (error) {
      console.error('Store: 保存表格数据失败:', error)
    }
  }

  // 从本地存储加载
  const loadFromStorage = () => {
    console.log('Store: 开始从本地存储加载数据')
    try {
      // 加载实习数据
      const internshipData = localStorage.getItem('hot-internship-data')
      console.log('Store: 实习数据localStorage值:', internshipData)
      if (internshipData) {
        const data = JSON.parse(internshipData)
        console.log('Store: 解析后的实习数据:', data)
        if (Array.isArray(data) && data.length > 0) {
          hotInternshipData.value.length = 0
          hotInternshipData.value.push(...data)
          console.log('Store: 从本地存储加载实习数据，共', data.length, '条记录')
        } else {
          console.log('Store: 实习数据为空或格式不正确')
        }
      } else {
        console.log('Store: 本地存储中没有实习数据')
      }

      // 加载国企数据
      const stateOwnedDataStored = localStorage.getItem('state-owned-data')
      console.log('Store: 国企数据localStorage值:', stateOwnedDataStored)
      if (stateOwnedDataStored) {
        const data = JSON.parse(stateOwnedDataStored)
        console.log('Store: 解析后的国企数据:', data)
        if (Array.isArray(data) && data.length > 0) {
          stateOwnedData.value.length = 0
          stateOwnedData.value.push(...data)
          console.log('Store: 从本地存储加载国企数据，共', data.length, '条记录')
        } else {
          console.log('Store: 国企数据为空或格式不正确')
        }
      } else {
        console.log('Store: 本地存储中没有国企数据')
      }

      console.log('Store: 加载完成后的数据状态:')
      console.log('Store: 实习数据条数:', hotInternshipData.value.length)
      console.log('Store: 国企数据条数:', stateOwnedData.value.length)
    } catch (error) {
      console.error('Store: 加载表格数据失败:', error)
    }
  }

  // 更新热门秋招数据
  const updateHotAutumnData = (data: RecruitmentSummary[]) => {
    console.log('Store: 更新热门秋招数据', data.length, '条记录')
    hotAutumnData.value.length = 0
    hotAutumnData.value.push(...data)
    console.log('Store: 热门秋招数据更新完成，当前数据条数:', hotAutumnData.value.length)
  }

  // 更新热门实习数据
  const updateHotInternshipData = (data: RecruitmentSummary[]) => {
    console.log('Store: 更新热门实习数据', data.length, '条记录')
    console.log('Store: 更新前实习数据条数:', hotInternshipData.value.length)
    hotInternshipData.value.length = 0
    hotInternshipData.value.push(...data)
    console.log('Store: 实习数据更新完成，当前数据条数:', hotInternshipData.value.length)

    // 保存到本地存储
    saveToStorage()
  }

  // 更新国企央企数据
  const updateStateOwnedData = (data: RecruitmentSummary[]) => {
    console.log('Store: 更新国企央企数据', data.length, '条记录')
    console.log('Store: 更新前国企数据条数:', stateOwnedData.value.length)
    stateOwnedData.value.length = 0
    stateOwnedData.value.push(...data)
    console.log('Store: 国企数据更新完成，当前数据条数:', stateOwnedData.value.length)

    // 保存到本地存储
    saveToStorage()
  }

  // 初始化默认数据
  const initializeDefaultData = () => {
    console.log('Store: 开始初始化默认数据')

    // 检查是否已经有数据（从本地存储加载的）
    console.log('Store: 初始化前数据状态:')
    console.log('Store: 实习数据条数:', hotInternshipData.value.length)
    console.log('Store: 国企数据条数:', stateOwnedData.value.length)

    // 如果本地存储没有数据，则使用默认数据
    // 热门实习默认数据
    const defaultInternshipData: RecruitmentSummary[] = [
      {
        id: 1001,
        updateTime: '2024-08-02',
        company: '字节跳动',
        applicationLink: 'https://jobs.bytedance.com/intern',
        industry: '互联网',
        tags: '技术实习',
        batch: '暑期实习',
        isHot: '热门',
        position: '前端开发实习生',
        location: '北京',
        deadline: '2024-09-30'
      },
      {
        id: 1002,
        updateTime: '2024-08-02',
        company: '腾讯',
        applicationLink: 'https://join.qq.com/intern',
        industry: '互联网',
        tags: '技术实习',
        batch: '暑期实习',
        isHot: '热门',
        position: '后端开发实习生',
        location: '深圳',
        deadline: '2024-09-15'
      },
      {
        id: 1003,
        updateTime: '2024-08-02',
        company: '阿里巴巴',
        applicationLink: 'https://talent.taobao.com/intern',
        industry: '互联网',
        tags: '技术实习',
        batch: '暑期实习',
        isHot: '推荐',
        position: '算法实习生',
        location: '杭州',
        deadline: '2024-10-01'
      }
    ]

    // 国企央企默认数据
    const defaultStateOwnedData: RecruitmentSummary[] = [
      {
        id: 2001,
        updateTime: '2024-08-02',
        company: '中国移动',
        applicationLink: 'https://www.10086.cn/jobs',
        industry: '通信',
        tags: '技术岗',
        batch: '秋招',
        isHot: '稳定',
        position: '软件开发工程师',
        location: '北京',
        deadline: '2024-11-30'
      },
      {
        id: 2002,
        updateTime: '2024-08-02',
        company: '国家电网',
        applicationLink: 'https://www.sgcc.com.cn/jobs',
        industry: '电力',
        tags: '技术岗',
        batch: '秋招',
        isHot: '稳定',
        position: '电力系统工程师',
        location: '上海',
        deadline: '2024-12-15'
      },
      {
        id: 2003,
        updateTime: '2024-08-02',
        company: '中国银行',
        applicationLink: 'https://www.boc.cn/jobs',
        industry: '金融',
        tags: '管理岗',
        batch: '秋招',
        isHot: '热门',
        position: '金融科技岗',
        location: '北京',
        deadline: '2024-11-20'
      },
      {
        id: 2004,
        updateTime: '2024-08-02',
        company: '中石油',
        applicationLink: 'https://www.cnpc.com.cn/jobs',
        industry: '能源',
        tags: '技术岗',
        batch: '秋招',
        isHot: '稳定',
        position: '石油工程师',
        location: '大庆',
        deadline: '2024-12-01'
      }
    ]

    // 只有在没有数据时才初始化默认数据
    if (hotInternshipData.value.length === 0) {
      console.log('Store: 实习数据为空，使用默认数据')
      hotInternshipData.value.push(...defaultInternshipData)
    } else {
      console.log('Store: 实习数据已存在，跳过默认数据初始化')
    }

    if (stateOwnedData.value.length === 0) {
      console.log('Store: 国企数据为空，使用默认数据')
      stateOwnedData.value.push(...defaultStateOwnedData)
    } else {
      console.log('Store: 国企数据已存在，跳过默认数据初始化')
    }

    console.log('Store: 默认数据初始化完成')
    console.log('Store: 最终实习数据条数:', hotInternshipData.value.length)
    console.log('Store: 最终国企数据条数:', stateOwnedData.value.length)
  }

  return {
    // 状态
    hotAutumnData,
    hotInternshipData,
    stateOwnedData,

    // 方法
    updateHotAutumnData,
    updateHotInternshipData,
    updateStateOwnedData,
    initializeDefaultData,
    saveToStorage,
    loadFromStorage
  }
})
