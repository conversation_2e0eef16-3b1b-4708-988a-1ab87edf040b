// 校招汇总表数据类型定义
export interface RecruitmentSummary {
  id: number
  updateTime?: string      // 更新时间
  company: string          // 公司
  applicationLink: string  // 投递链接
  industry: string         // 行业
  tags: string            // 标签
  batch: string           // 批次
  isHot: string           // 热门（改为字符串类型）
  position: string        // 职位
  location: string        // 地点
  deadline: string        // 投递截至
}

// 行业选项
export const industryOptions = [
  { label: '互联网', value: '互联网' },
  { label: '金融', value: '金融' },
  { label: '制造业', value: '制造业' },
  { label: '房地产', value: '房地产' },
  { label: '教育', value: '教育' },
  { label: '医疗', value: '医疗' },
  { label: '零售', value: '零售' },
  { label: '物流', value: '物流' },
  { label: '能源', value: '能源' },
  { label: '其他', value: '其他' }
]

// 批次选项
export const batchOptions = [
  { label: '提前批', value: '提前批' },
  { label: '秋招', value: '秋招' },
  { label: '春招', value: '春招' },
  { label: '日常招聘', value: '日常招聘' },
  { label: '补录', value: '补录' }
]

// 标签选项
export const tagOptions = [
  { label: '技术岗', value: '技术岗' },
  { label: '产品岗', value: '产品岗' },
  { label: '运营岗', value: '运营岗' },
  { label: '市场岗', value: '市场岗' },
  { label: '销售岗', value: '销售岗' },
  { label: '职能岗', value: '职能岗' },
  { label: '管培生', value: '管培生' },
  { label: '实习生', value: '实习生' }
]
