{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 3548338242169704075, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 11801558257907785920], [3150220818285335163, "url", false, 3140987579113747913], [3191507132440681679, "serde_untagged", false, 16823632316559890524], [4071963112282141418, "serde_with", false, 2898557648920224799], [4899080583175475170, "semver", false, 5591279422727034763], [5986029879202738730, "log", false, 14425135604263769896], [6606131838865521726, "ctor", false, 14613437286463165985], [7170110829644101142, "json_patch", false, 8926927892608597346], [8319709847752024821, "uuid", false, 7882582260335461401], [9010263965687315507, "http", false, 10156329732527234393], [9090328626728818999, "toml", false, 10215724057794548929], [9451456094439810778, "regex", false, 14023196224415044092], [9556762810601084293, "brotli", false, 586860974432276487], [9689903380558560274, "serde", false, 4567830511950114312], [10806645703491011684, "thiserror", false, 798670949607384445], [11989259058781683633, "dunce", false, 7882815010359764019], [13625485746686963219, "anyhow", false, 13014709620378321028], [15622660310229662834, "walkdir", false, 6136017899532400], [15932120279885307830, "memchr", false, 14117915976406651097], [16362055519698394275, "serde_json", false, 1560433525378758638], [17146114186171651583, "infer", false, 4694820463547926759], [17155886227862585100, "glob", false, 480147955432837878], [17186037756130803222, "phf", false, 13924943256531489273]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-3e797729968bbced\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}