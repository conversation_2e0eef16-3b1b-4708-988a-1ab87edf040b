{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 8780018067511518976], [12092653563678505622, "build_script_build", false, 17052202899347285268], [8324462083842905811, "build_script_build", false, 10647878718970155197], [2784153353110520258, "build_script_build", false, 17540127617612973611]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-a37f8de607671cfd\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}