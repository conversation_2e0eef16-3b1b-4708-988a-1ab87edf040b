# 飞书多维表格集成配置指南

## 概述

本应用支持从飞书多维表格同步校招数据，实现数据的自动更新和管理。

## 前置条件

1. 拥有飞书账号
2. 创建了包含校招数据的多维表格
3. 在飞书开放平台创建了应用

## 配置步骤

### 1. 创建飞书开放平台应用

1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 登录并进入"开发者后台"
3. 点击"创建应用" → "自建应用"
4. 填写应用信息：
   - 应用名称：如"校招数据同步"
   - 应用描述：校招信息管理应用
   - 应用图标：可选
5. 创建完成后，记录 `App ID` 和 `App Secret`

### 2. 配置应用权限

在应用管理页面：

1. 进入"权限管理"
2. 添加以下权限：
   - `bitable:app` - 获取多维表格信息
   - `bitable:app:readonly` - 读取多维表格
   - `bitable:app:table` - 访问数据表
   - `bitable:app:table:record` - 读取记录
   - `bitable:app:table:record:readonly` - 只读记录权限

### 3. 获取应用凭证

在应用管理页面：

1. 进入"凭证与基础信息"
2. 记录以下信息：
   - **App ID**: 应用唯一标识
   - **App Secret**: 应用密钥

**注意**: 应用会自动使用这些凭证获取 `tenant_access_token` 来访问多维表格

### 4. 获取多维表格信息

#### 获取 App Token

**方法一：从开发者选项获取（推荐）**
1. 打开你的飞书多维表格
2. 点击右上角"..."菜单
3. 选择"高级" → "开发者选项"
4. 复制 `App Token`

**方法二：从URL中提取**
1. 在多维表格中，复制浏览器地址栏的URL
2. 从URL中提取App Token

示例URL：
```
https://example.feishu.cn/base/RR9cb8uqKaBUOGso9WTcBiXjnfh/tblxxx.../viwxxx...
                           ↑ 这部分是 App Token
```

**App Token格式**：`RR9cb8uqKaBUOGso9WTcBiXjnfh`（不包含`base/`前缀）

#### 获取 Table ID

1. 在多维表格中，右键点击数据表标签
2. 选择"复制表格链接"
3. 从链接中提取 Table ID

示例链接：
```
https://example.feishu.cn/base/RR9cb8uqKaBUOGso9WTcBiXjnfh/tblxxx.../viwxxx...
                           ↑ App Token                    ↑ Table ID
```

**Table ID格式**：`tblxxx...`（以`tbl`开头）

### 5. 配置字段映射

确保飞书多维表格包含以下字段（可自定义字段名）：

| 本地字段 | 建议的飞书字段名 | 字段类型 | 说明 |
|----------|------------------|----------|------|
| 公司 | 公司 | 单行文本 | 公司名称 |
| 投递链接 | 投递链接 | URL | 职位申请链接 |
| 行业 | 行业 | 单选 | 所属行业 |
| 标签 | 标签 | 多选 | 职位标签 |
| 批次 | 批次 | 单选 | 招聘批次 |
| 热门 | 热门 | 复选框 | 是否热门职位 |
| 职位 | 职位 | 单行文本 | 职位名称 |
| 地点 | 地点 | 单行文本 | 工作地点 |
| 投递截至 | 投递截至 | 日期 | 申请截止日期 |

## 应用内配置

### 1. 进入设置页面

1. 打开应用
2. 点击左侧菜单"⚙️ 设置"

### 2. 填写飞书配置

在"飞书多维表格配置"区域填写：

- **App Token**: 从多维表格获取的应用令牌
- **Table ID**: 数据表的唯一标识
- **App ID**: 飞书开放平台应用的唯一标识
- **App Secret**: 飞书开放平台应用的密钥

### 3. 配置字段映射

在"字段映射配置"区域：

1. 根据你的飞书表格实际字段名配置映射关系
2. 确保字段名完全匹配（区分大小写）
3. 点击"保存字段映射"

### 4. 测试连接

1. 点击"测试连接"按钮
2. 确认连接成功

### 5. 同步数据

1. 点击"🔄 更新数据"按钮
2. 等待数据同步完成
3. 切换到"校招汇总表"查看同步的数据

## 常见问题

### Q: 提示"连接测试失败"怎么办？

A: 请检查：
1. App Token 和 Table ID 是否正确
2. App ID 和 App Secret 是否正确
3. 应用权限是否配置正确
4. 网络连接是否正常

### Q: 数据同步后字段为空？

A: 请检查：
1. 字段映射配置是否正确
2. 飞书表格中的字段名是否与配置一致
3. 字段名是否区分大小写

### Q: tenant_access_token 过期怎么办？

A: 应用会自动管理 tenant_access_token：
1. 自动检测token过期时间
2. 在过期前自动刷新token
3. 无需手动干预
4. 如果App Secret变更，需要在设置页面更新

### Q: 支持哪些字段类型？

A: 目前支持：
- 单行文本
- 多行文本
- 数字
- 日期
- 单选
- 多选
- 复选框
- URL

## 安全建议

1. **不要在代码中硬编码敏感信息**
2. **定期更新 Access Token**
3. **限制应用权限范围**
4. **使用 HTTPS 进行 API 调用**
5. **妥善保管 App Secret**

## API 限制

- 单次最多获取 500 条记录
- 应用有调用频率限制
- 大量数据建议分批同步

## 更多资源

- [飞书开放平台文档](https://open.feishu.cn/document/)
- [多维表格 API 文档](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-record/list)
- [权限申请指南](https://open.feishu.cn/document/ukTMukTMukTM/uYTM5UjL2ETO14iNxkTN/scope-list)
