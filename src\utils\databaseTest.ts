// 数据库测试工具
import { db, databaseService } from '../services/database'

export class DatabaseTester {
  
  // 测试数据库连接
  static async testConnection(): Promise<boolean> {
    try {
      console.log('测试数据库连接...')
      
      if (!db.isOpen()) {
        await db.open()
      }
      
      // 尝试执行一个简单的查询
      await db.userSettings.count()
      console.log('数据库连接测试成功')
      return true
    } catch (error) {
      console.error('数据库连接测试失败:', error)
      return false
    }
  }
  
  // 测试数据库操作
  static async testOperations(): Promise<boolean> {
    try {
      console.log('测试数据库操作...')
      
      // 测试设置操作
      await databaseService.setSetting('test_key', 'test_value')
      const value = await databaseService.getSetting('test_key')
      
      if (value !== 'test_value') {
        throw new Error('设置读写测试失败')
      }
      
      // 清理测试数据
      await db.userSettings.where('key').equals('test_key').delete()
      
      console.log('数据库操作测试成功')
      return true
    } catch (error) {
      console.error('数据库操作测试失败:', error)
      return false
    }
  }
  
  // 模拟数据库关闭错误
  static async simulateDatabaseError(): Promise<void> {
    try {
      console.log('模拟数据库关闭...')
      db.close()
      
      // 尝试执行操作，应该会触发错误
      await databaseService.setSetting('test_after_close', 'value')
    } catch (error) {
      console.log('成功捕获数据库关闭错误:', error)
    }
  }
  
  // 完整的数据库健康检查
  static async fullHealthCheck(): Promise<{
    connection: boolean
    operations: boolean
    tables: Record<string, number>
  }> {
    const result = {
      connection: false,
      operations: false,
      tables: {} as Record<string, number>
    }
    
    try {
      // 连接测试
      result.connection = await this.testConnection()
      
      // 操作测试
      if (result.connection) {
        result.operations = await this.testOperations()
        
        // 表数据统计
        result.tables = {
          userSettings: await db.userSettings.count(),
          feishuConfig: await db.feishuConfig.count(),
          fieldMappings: await db.fieldMappings.count(),
          jobApplications: await db.jobApplications.count(),
          hotAutumnRecruitments: await db.hotAutumnRecruitments.count(),
          hotInternshipRecruitments: await db.hotInternshipRecruitments.count(),
          stateOwnedRecruitments: await db.stateOwnedRecruitments.count()
        }
      }
    } catch (error) {
      console.error('健康检查失败:', error)
    }
    
    return result
  }
}

// 在开发环境下暴露到全局，便于调试
if (process.env.NODE_ENV === 'development') {
  (window as any).DatabaseTester = DatabaseTester
}
