import { defineStore } from 'pinia'
import { ref } from 'vue'
import { databaseService, db } from '../services/database'
import { defaultFeishuConfig, saveConfigToStorage } from '../services/feishuApi'
import type { RecruitmentSummary } from '../types/recruitment'
import type { JobApplication } from '../types/jobApplication'

export const useDatabaseStore = defineStore('database', () => {
  // 数据状态
  const hotAutumnData = ref<RecruitmentSummary[]>([])
  const hotInternshipData = ref<RecruitmentSummary[]>([])
  const stateOwnedData = ref<RecruitmentSummary[]>([])
  const jobApplications = ref<JobApplication[]>([])
  
  // 加载状态
  const loading = ref(false)
  const initialized = ref(false)

  // ==================== 初始化 ====================

  // 初始化默认配置
  const initializeDefaultConfig = async () => {
    try {
      const existingConfig = await databaseService.getFeishuConfig()
      if (!existingConfig) {
        console.log('DatabaseStore: 设置默认飞书配置')
        await databaseService.saveFeishuConfig(defaultFeishuConfig)
        saveConfigToStorage(defaultFeishuConfig)
        console.log('DatabaseStore: 默认飞书配置已设置')
      }
    } catch (error) {
      console.error('DatabaseStore: 初始化默认配置失败:', error)
    }
  }

  // 数据库健康检查和重连
  const checkDatabaseHealth = async () => {
    try {
      if (!db.isOpen()) {
        console.log('DatabaseStore: 数据库未打开，尝试重新连接...')
        await db.open()
        console.log('DatabaseStore: 数据库重新连接成功')
        initialized.value = true
        return true
      }
      return true
    } catch (error) {
      console.error('DatabaseStore: 数据库健康检查失败:', error)
      initialized.value = false
      return false
    }
  }

  // 重新初始化数据库
  const reinitializeDatabase = async () => {
    console.log('DatabaseStore: 开始重新初始化数据库')
    initialized.value = false
    loading.value = true

    try {
      // 关闭现有连接
      if (db.isOpen()) {
        await db.close()
      }

      // 重新打开数据库
      await db.open()
      console.log('DatabaseStore: 数据库重新连接成功')

      // 重新加载数据
      await Promise.all([
        loadHotAutumnData(),
        loadHotInternshipData(),
        loadStateOwnedData(),
        loadJobApplications()
      ])

      initialized.value = true
      console.log('DatabaseStore: 数据库重新初始化完成')
      return true
    } catch (error) {
      console.error('DatabaseStore: 数据库重新初始化失败:', error)
      initialized.value = false
      return false
    } finally {
      loading.value = false
    }
  }
  
  const initializeDatabase = async () => {
    if (initialized.value) return

    console.log('DatabaseStore: 开始初始化数据库')
    console.log('DatabaseStore: 数据库名称:', db.name)
    console.log('DatabaseStore: 是否为生产环境:', process.env.NODE_ENV === 'production')

    loading.value = true

    try {
      // 确保数据库正确打开
      if (!db.isOpen()) {
        await db.open()
        console.log('DatabaseStore: 数据库连接成功')
      }

      await Promise.all([
        loadHotAutumnData(),
        loadHotInternshipData(),
        loadStateOwnedData(),
        loadJobApplications(),
        initializeDefaultConfig()
      ])

      initialized.value = true
      console.log('DatabaseStore: 数据库初始化完成')
    } catch (error) {
      console.error('DatabaseStore: 数据库初始化失败:', error)
      // 初始化失败时，尝试从 localStorage 恢复基本功能
      console.log('DatabaseStore: 尝试从 localStorage 恢复数据')
      initialized.value = false
    } finally {
      loading.value = false
    }
  }

  // ==================== 热门秋招汇总 ====================
  
  const loadHotAutumnData = async () => {
    try {
      const data = await databaseService.getAllHotAutumnRecruitments()
      hotAutumnData.value = data
      console.log(`DatabaseStore: 加载热门秋招数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 加载热门秋招数据失败:', error)
    }
  }

  const saveHotAutumnData = async (data: RecruitmentSummary[]) => {
    try {
      // 检查数据库健康状态
      const isHealthy = await checkDatabaseHealth()
      if (!isHealthy) {
        console.log('DatabaseStore: 数据库不健康，尝试重新初始化...')
        const reinitSuccess = await reinitializeDatabase()
        if (!reinitSuccess) {
          throw new Error('数据库重新初始化失败')
        }
      }

      await databaseService.saveHotAutumnRecruitments(data)
      hotAutumnData.value = [...data]
      console.log(`DatabaseStore: 保存热门秋招数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 保存热门秋招数据失败:', error)

      // 如果是数据库关闭错误，尝试重新初始化
      if (error instanceof Error && (error.name === 'DatabaseClosedError' || error.message?.includes('DatabaseClosedError'))) {
        console.log('DatabaseStore: 检测到数据库关闭错误，尝试重新初始化...')
        try {
          await reinitializeDatabase()
          await databaseService.saveHotAutumnRecruitments(data)
          hotAutumnData.value = [...data]
          console.log(`DatabaseStore: 重新初始化后保存成功 ${data.length} 条`)
          return
        } catch (retryError) {
          console.error('DatabaseStore: 重新初始化后仍然失败:', retryError)
        }
      }

      throw error
    }
  }

  const updateHotAutumnRecord = async (id: number, updates: Partial<RecruitmentSummary>) => {
    try {
      await databaseService.updateHotAutumnRecruitment(id, updates)
      const index = hotAutumnData.value.findIndex(item => item.id === id)
      if (index !== -1) {
        hotAutumnData.value[index] = { ...hotAutumnData.value[index], ...updates }
      }
      console.log(`DatabaseStore: 更新热门秋招记录 ID ${id}`)
    } catch (error) {
      console.error('DatabaseStore: 更新热门秋招记录失败:', error)
      throw error
    }
  }

  // ==================== 热门实习汇总 ====================
  
  const loadHotInternshipData = async () => {
    try {
      const data = await databaseService.getAllHotInternshipRecruitments()
      hotInternshipData.value = data
      console.log(`DatabaseStore: 加载热门实习数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 加载热门实习数据失败:', error)
    }
  }

  const saveHotInternshipData = async (data: RecruitmentSummary[]) => {
    try {
      await databaseService.saveHotInternshipRecruitments(data)
      hotInternshipData.value = [...data]
      console.log(`DatabaseStore: 保存热门实习数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 保存热门实习数据失败:', error)
      throw error
    }
  }

  const updateHotInternshipRecord = async (id: number, updates: Partial<RecruitmentSummary>) => {
    try {
      await databaseService.updateHotInternshipRecruitment(id, updates)
      const index = hotInternshipData.value.findIndex(item => item.id === id)
      if (index !== -1) {
        hotInternshipData.value[index] = { ...hotInternshipData.value[index], ...updates }
      }
      console.log(`DatabaseStore: 更新热门实习记录 ID ${id}`)
    } catch (error) {
      console.error('DatabaseStore: 更新热门实习记录失败:', error)
      throw error
    }
  }

  // ==================== 国企央企汇总 ====================
  
  const loadStateOwnedData = async () => {
    try {
      const data = await databaseService.getAllStateOwnedRecruitments()
      stateOwnedData.value = data
      console.log(`DatabaseStore: 加载国企央企数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 加载国企央企数据失败:', error)
    }
  }

  const saveStateOwnedData = async (data: RecruitmentSummary[]) => {
    try {
      await databaseService.saveStateOwnedRecruitments(data)
      stateOwnedData.value = [...data]
      console.log(`DatabaseStore: 保存国企央企数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 保存国企央企数据失败:', error)
      throw error
    }
  }

  const updateStateOwnedRecord = async (id: number, updates: Partial<RecruitmentSummary>) => {
    try {
      await databaseService.updateStateOwnedRecruitment(id, updates)
      const index = stateOwnedData.value.findIndex(item => item.id === id)
      if (index !== -1) {
        stateOwnedData.value[index] = { ...stateOwnedData.value[index], ...updates }
      }
      console.log(`DatabaseStore: 更新国企央企记录 ID ${id}`)
    } catch (error) {
      console.error('DatabaseStore: 更新国企央企记录失败:', error)
      throw error
    }
  }

  // ==================== 求职申请 ====================
  
  const loadJobApplications = async () => {
    try {
      const data = await databaseService.getAllJobApplications()
      jobApplications.value = data
      console.log(`DatabaseStore: 加载求职申请数据 ${data.length} 条`)
    } catch (error) {
      console.error('DatabaseStore: 加载求职申请数据失败:', error)
    }
  }

  const addJobApplication = async (application: Omit<JobApplication, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const id = await databaseService.addJobApplication(application)
      await loadJobApplications() // 重新加载数据
      console.log(`DatabaseStore: 添加求职申请 ID ${id}`)
      return id
    } catch (error) {
      console.error('DatabaseStore: 添加求职申请失败:', error)
      throw error
    }
  }

  const updateJobApplication = async (id: number, updates: Partial<JobApplication>) => {
    try {
      await databaseService.updateJobApplication(id, updates)
      const index = jobApplications.value.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        jobApplications.value[index] = { ...jobApplications.value[index], ...updates }
      }
      console.log(`DatabaseStore: 更新求职申请 ID ${id}`)
    } catch (error) {
      console.error('DatabaseStore: 更新求职申请失败:', error)
      throw error
    }
  }

  const deleteJobApplication = async (id: number) => {
    try {
      await databaseService.deleteJobApplication(id)
      const index = jobApplications.value.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        jobApplications.value.splice(index, 1)
      }
      console.log(`DatabaseStore: 删除求职申请 ID ${id}`)
    } catch (error) {
      console.error('DatabaseStore: 删除求职申请失败:', error)
      throw error
    }
  }

  // ==================== 数据管理 ====================
  
  const clearAllData = async () => {
    try {
      await databaseService.clearAllData()
      hotAutumnData.value = []
      hotInternshipData.value = []
      stateOwnedData.value = []
      jobApplications.value = []
      console.log('DatabaseStore: 所有数据已清空')
    } catch (error) {
      console.error('DatabaseStore: 清空数据失败:', error)
      throw error
    }
  }

  const exportAllData = async () => {
    try {
      return await databaseService.exportAllData()
    } catch (error) {
      console.error('DatabaseStore: 导出数据失败:', error)
      throw error
    }
  }

  const getDataStats = async () => {
    try {
      return await databaseService.getDataStats()
    } catch (error) {
      console.error('DatabaseStore: 获取数据统计失败:', error)
      return {}
    }
  }

  // ==================== 设置管理 ====================
  
  const getSetting = async (key: string) => {
    try {
      return await databaseService.getSetting(key)
    } catch (error) {
      console.error(`DatabaseStore: 获取设置 ${key} 失败:`, error)
      return null
    }
  }

  const setSetting = async (key: string, value: string) => {
    try {
      await databaseService.setSetting(key, value)
      console.log(`DatabaseStore: 设置已保存 ${key} = ${value}`)
    } catch (error) {
      console.error(`DatabaseStore: 保存设置 ${key} 失败:`, error)
      throw error
    }
  }

  // ==================== 飞书配置 ====================
  
  const getFeishuConfig = async () => {
    try {
      return await databaseService.getFeishuConfig()
    } catch (error) {
      console.error('DatabaseStore: 获取飞书配置失败:', error)
      return null
    }
  }

  const saveFeishuConfig = async (config: any) => {
    try {
      await databaseService.saveFeishuConfig(config)
      console.log('DatabaseStore: 飞书配置已保存')
    } catch (error) {
      console.error('DatabaseStore: 保存飞书配置失败:', error)
      throw error
    }
  }

  // ==================== 字段映射 ====================
  
  const getFieldMappings = async () => {
    try {
      return await databaseService.getFieldMappings()
    } catch (error) {
      console.error('DatabaseStore: 获取字段映射失败:', error)
      return {}
    }
  }

  const saveFieldMappings = async (mappings: Record<string, string>) => {
    try {
      await databaseService.saveFieldMappings(mappings)
      console.log('DatabaseStore: 字段映射已保存')
    } catch (error) {
      console.error('DatabaseStore: 保存字段映射失败:', error)
      throw error
    }
  }

  return {
    // 状态
    hotAutumnData,
    hotInternshipData,
    stateOwnedData,
    jobApplications,
    loading,
    initialized,

    // 初始化
    initializeDatabase,
    checkDatabaseHealth,
    reinitializeDatabase,

    // 热门秋招汇总
    loadHotAutumnData,
    saveHotAutumnData,
    updateHotAutumnRecord,

    // 热门实习汇总
    loadHotInternshipData,
    saveHotInternshipData,
    updateHotInternshipRecord,

    // 国企央企汇总
    loadStateOwnedData,
    saveStateOwnedData,
    updateStateOwnedRecord,

    // 求职申请
    loadJobApplications,
    addJobApplication,
    updateJobApplication,
    deleteJobApplication,

    // 数据管理
    clearAllData,
    exportAllData,
    getDataStats,

    // 设置管理
    getSetting,
    setSetting,

    // 飞书配置
    getFeishuConfig,
    saveFeishuConfig,

    // 字段映射
    getFieldMappings,
    saveFieldMappings
  }
})
