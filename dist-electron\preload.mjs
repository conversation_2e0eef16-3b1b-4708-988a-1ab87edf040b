"use strict";const n=require("electron");n.contextBridge.exposeInMainWorld("ipcRenderer",{on(...e){const[i,r]=e;return n.ipcRenderer.on(i,(o,...c)=>r(o,...c))},off(...e){const[i,...r]=e;return n.ipcRenderer.off(i,...r)},send(...e){const[i,...r]=e;return n.ipcRenderer.send(i,...r)},invoke(...e){const[i,...r]=e;return n.ipcRenderer.invoke(i,...r)}});n.contextBridge.exposeInMainWorld("electronAPI",{minimize:()=>n.ipcRenderer.invoke("window-minimize"),maximize:()=>n.ipcRenderer.invoke("window-maximize"),close:()=>n.ipcRenderer.invoke("window-close"),openExternal:e=>n.ipcRenderer.invoke("open-external",e),feishuApiRequest:e=>n.ipcRenderer.invoke("feishu-api-request",e)});
