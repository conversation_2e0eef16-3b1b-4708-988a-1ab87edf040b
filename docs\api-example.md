# 飞书多维表格API调用示例

## 使用tenant_access_token访问多维表格

### 1. 获取tenant_access_token

```javascript
async function getTenantAccessToken(appId, appSecret) {
  const response = await fetch('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      app_id: appId,
      app_secret: appSecret
    })
  });

  const data = await response.json();
  
  if (data.code !== 0) {
    throw new Error(`获取token失败: ${data.msg}`);
  }

  return {
    token: data.tenant_access_token,
    expire: data.expire
  };
}
```

### 2. 使用token访问多维表格

```javascript
async function getTableRecords(appToken, tableId, tenantAccessToken) {
  const response = await fetch(
    `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables/${tableId}/records?page_size=500`,
    {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${tenantAccessToken}`,
        'Content-Type': 'application/json'
      }
    }
  );

  const data = await response.json();
  
  if (data.code !== 0) {
    throw new Error(`获取记录失败: ${data.msg}`);
  }

  return data.data.items;
}
```

### 3. 完整的使用示例

```javascript
class FeishuBitableClient {
  constructor(appId, appSecret) {
    this.appId = appId;
    this.appSecret = appSecret;
    this.tenantAccessToken = null;
    this.tokenExpireTime = 0;
  }

  async ensureValidToken() {
    const now = Date.now();
    if (this.tenantAccessToken && this.tokenExpireTime > now) {
      return this.tenantAccessToken;
    }

    const tokenData = await getTenantAccessToken(this.appId, this.appSecret);
    this.tenantAccessToken = tokenData.token;
    // 提前5分钟过期
    this.tokenExpireTime = now + (tokenData.expire - 300) * 1000;
    
    return this.tenantAccessToken;
  }

  async getRecords(appToken, tableId) {
    const token = await this.ensureValidToken();
    return await getTableRecords(appToken, tableId, token);
  }

  async getAllRecords(appToken, tableId) {
    const allRecords = [];
    let pageToken = null;
    let hasMore = true;

    while (hasMore) {
      const token = await this.ensureValidToken();
      const url = `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables/${tableId}/records?page_size=500${pageToken ? `&page_token=${pageToken}` : ''}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.code !== 0) {
        throw new Error(`获取记录失败: ${data.msg}`);
      }

      allRecords.push(...data.data.items);
      hasMore = data.data.has_more;
      pageToken = data.data.page_token;
    }

    return allRecords;
  }
}
```

### 4. 使用方法

```javascript
// 初始化客户端
const client = new FeishuBitableClient('your_app_id', 'your_app_secret');

// 获取记录
try {
  const records = await client.getAllRecords('your_app_token', 'your_table_id');
  console.log(`获取到 ${records.length} 条记录`);
  
  // 处理记录
  records.forEach(record => {
    console.log('记录ID:', record.record_id);
    console.log('字段数据:', record.fields);
  });
} catch (error) {
  console.error('获取数据失败:', error.message);
}
```

## 权限要求

确保你的应用具有以下权限：

- `bitable:app` - 获取多维表格信息
- `bitable:app:readonly` - 读取多维表格
- `bitable:app:table` - 访问数据表
- `bitable:app:table:record` - 读取记录
- `bitable:app:table:record:readonly` - 只读记录权限

## 注意事项

1. **token有效期**: tenant_access_token有效期为2小时
2. **自动刷新**: 建议在token过期前5分钟自动刷新
3. **错误处理**: 始终检查API响应的code字段
4. **分页处理**: 单次最多返回500条记录，需要处理分页
5. **频率限制**: 注意API调用频率限制

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 99991663 | 应用未安装 | 确保应用已安装到对应租户 |
| 99991664 | 无权限访问 | 检查应用权限配置 |
| 99991665 | token无效 | 重新获取token |
| 99991666 | 参数错误 | 检查请求参数 |

## 最佳实践

1. **缓存token**: 避免频繁获取token
2. **错误重试**: 实现指数退避重试机制
3. **并发控制**: 避免过多并发请求
4. **数据验证**: 验证返回的数据格式
5. **日志记录**: 记录API调用日志便于调试
