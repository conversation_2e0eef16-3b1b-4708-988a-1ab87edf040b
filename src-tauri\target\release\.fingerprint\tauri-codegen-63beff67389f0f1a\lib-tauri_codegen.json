{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601************, "path": 10518571048255124941, "deps": [[654232091421095663, "tauri_utils", false, 15940897645434972860], [3060637413840920116, "proc_macro2", false, 8292096060766751894], [3150220818285335163, "url", false, 12030816734033915076], [4899080583175475170, "semver", false, 11149204527497062953], [4974441333307933176, "syn", false, 6948136686291041943], [7170110829644101142, "json_patch", false, 8354039892772010322], [7392050791754369441, "ico", false, 16837076623243135849], [8319709847752024821, "uuid", false, 10900895984594107327], [9556762810601084293, "brotli", false, 2406466242898887549], [9689903380558560274, "serde", false, 14456609809314144097], [9857275760291862238, "sha2", false, 1545507959360672492], [10806645703491011684, "thiserror", false, 15568306591736192511], [12687914511023397207, "png", false, 272403013969597021], [13077212702700853852, "base64", false, 4003372118664827581], [15622660310229662834, "walkdir", false, 7056154628979218839], [16362055519698394275, "serde_json", false, 10160787341787251069], [17990358020177143287, "quote", false, 13438475266858967644]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-63beff67389f0f1a\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}