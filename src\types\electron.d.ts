// Electron API 类型声明

export interface ElectronAPI {
  minimize: () => Promise<void>
  maximize: () => Promise<void>
  close: () => Promise<void>
  openExternal: (url: string) => Promise<void>
  feishuApiRequest: (options: {
    url: string
    method: string
    headers?: Record<string, string>
    body?: string
  }) => Promise<{
    ok: boolean
    status: number
    statusText: string
    data: any
  }>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export {}
