{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 2634691503022098402, "deps": [[654232091421095663, "tauri_utils", false, 15940897645434972860], [2704937418414716471, "tauri_codegen", false, 13255615769947434757], [3060637413840920116, "proc_macro2", false, 8292096060766751894], [4974441333307933176, "syn", false, 6948136686291041943], [13077543566650298139, "heck", false, 15790567292491778291], [17990358020177143287, "quote", false, 13438475266858967644]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-0423d6bf8f3d15ea\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}