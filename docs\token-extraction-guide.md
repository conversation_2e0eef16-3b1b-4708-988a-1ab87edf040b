# 飞书多维表格 Token 提取指南

## App Token 和 Table ID 的正确格式

### 完整的飞书多维表格URL示例

```
https://bytedance.feishu.cn/base/RR9cb8uqKaBUOGso9WTcBiXjnfh/tblxxx123456789/viwxxx987654321
```

### URL结构解析

```
https://[域名]/base/[App Token]/[Table ID]/[View ID]
         ↓        ↓           ↓          ↓
    feishu.cn   App Token   Table ID   View ID
```

## 提取方法

### 方法一：从开发者选项获取 App Token（推荐）

1. **打开飞书多维表格**
2. **点击右上角"..."菜单**
3. **选择"高级" → "开发者选项"**
4. **复制显示的 App Token**

✅ **优点**: 直接获取，不会出错
❌ **缺点**: 需要权限访问开发者选项

### 方法二：从URL提取

#### 步骤1：获取完整URL
1. 在浏览器中打开飞书多维表格
2. 复制地址栏的完整URL

#### 步骤2：提取App Token
从URL中提取 `/base/` 后面的部分，直到下一个 `/` 为止

**示例**：
```
原始URL: https://bytedance.feishu.cn/base/RR9cb8uqKaBUOGso9WTcBiXjnfh/tblxxx123456789/viwxxx987654321

App Token: RR9cb8uqKaBUOGso9WTcBiXjnfh
          ↑ 从这里开始                ↑ 到这里结束
```

#### 步骤3：提取Table ID
从App Token后面的 `/` 开始，到下一个 `/` 为止

**示例**：
```
原始URL: https://bytedance.feishu.cn/base/RR9cb8uqKaBUOGso9WTcBiXjnfh/tblxxx123456789/viwxxx987654321

Table ID: tblxxx123456789
         ↑ 从这里开始    ↑ 到这里结束
```

## 常见格式

### App Token 格式特征
- **长度**: 通常22-27个字符
- **字符**: 包含大小写字母和数字
- **开头**: 通常以大写字母开头
- **示例**: `RR9cb8uqKaBUOGso9WTcBiXjnfh`

### Table ID 格式特征
- **前缀**: 始终以 `tbl` 开头
- **长度**: `tbl` + 12-15个字符
- **字符**: 包含字母、数字
- **示例**: `tblxxx123456789`

## 错误示例 ❌

### App Token 错误格式
```
❌ base/RR9cb8uqKaBUOGso9WTcBiXjnfh  (包含了base/前缀)
❌ /RR9cb8uqKaBUOGso9WTcBiXjnfh     (包含了/前缀)
❌ RR9cb8uqKaBUOGso9WTcBiXjnfh/     (包含了/后缀)
✅ RR9cb8uqKaBUOGso9WTcBiXjnfh      (正确格式)
```

### Table ID 错误格式
```
❌ /tblxxx123456789    (包含了/前缀)
❌ tblxxx123456789/    (包含了/后缀)
✅ tblxxx123456789     (正确格式)
```

## 验证方法

### 在应用中验证
1. 在设置页面填入提取的Token
2. 点击"测试连接"按钮
3. 如果连接成功，说明Token正确

### 手动验证API调用
```bash
# 使用curl测试（需要先获取tenant_access_token）
curl -X GET \
  "https://open.feishu.cn/open-apis/bitable/v1/apps/RR9cb8uqKaBUOGso9WTcBiXjnfh/tables/tblxxx123456789/records?page_size=1" \
  -H "Authorization: Bearer YOUR_TENANT_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

## 常见问题

### Q: 提示"应用未安装"错误
A: 检查App Token是否正确，确保没有包含多余的前缀或后缀

### Q: 提示"表格不存在"错误  
A: 检查Table ID是否正确，确保以"tbl"开头

### Q: 无法访问开发者选项
A: 使用URL提取方法，或联系表格管理员获取权限

### Q: URL中没有看到预期的格式
A: 确保你在多维表格页面，而不是在文档或其他页面

## 最佳实践

1. **优先使用开发者选项**: 最准确，不容易出错
2. **仔细检查格式**: 确保没有多余的字符
3. **测试连接**: 配置后立即测试
4. **保存配置**: 测试成功后及时保存
5. **文档记录**: 记录正确的Token以备后用

## 安全提醒

- **不要分享App Token**: 它可以访问你的多维表格数据
- **定期检查权限**: 确保只有授权人员可以访问
- **使用最小权限**: 只申请必要的API权限
- **安全存储**: 不要在代码中硬编码Token
