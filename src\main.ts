import { createApp } from 'vue'
import './style.css'
import App from './App.vue'

// Import TDesign Vue Next
import TDesign from 'tdesign-vue-next'
import 'tdesign-vue-next/es/style/index.css'

// Import Pinia
import { createPinia } from 'pinia'

const app = createApp(App)
const pinia = createPinia()

// Use Pinia
app.use(pinia)

// Use TDesign
app.use(TDesign)

app.mount('#app').$nextTick(() => {
  // Use contextBridge
  window.ipcRenderer.on('main-process-message', (_event, message) => {
    console.log(message)
  })
})
