{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 993936270131575788, "deps": [[376837177317575824, "softbuffer", false, 12349875331361740306], [654232091421095663, "tauri_utils", false, 12011921763369382356], [2013030631243296465, "webview2_com", false, 8179563293025496140], [3150220818285335163, "url", false, 3140987579113747913], [3722963349756955755, "once_cell", false, 16660087782530539821], [4143744114649553716, "raw_window_handle", false, 9730845076325814965], [5986029879202738730, "log", false, 14425135604263769896], [8826339825490770380, "tao", false, 5333387410735830473], [9010263965687315507, "http", false, 10156329732527234393], [9141053277961803901, "wry", false, 10769653235128999780], [12304025191202589669, "build_script_build", false, 10247289867156551929], [12943761728066819757, "tauri_runtime", false, 12546026217587187799], [14585479307175734061, "windows", false, 2741241964980341311]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-85500ce00f2e3864\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}