import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { RecruitmentSummary } from '../types/recruitment'

export const useRecruitmentStore = defineStore('recruitment', () => {
  // 状态
  const recruitments = ref<RecruitmentSummary[]>([])
  const loading = ref(false)

  // 初始化默认数据
  const initializeDefaultData = () => {
    const defaultData: RecruitmentSummary[] = [
      {
        id: 1,
        updateTime: '2024-08-01',
        company: '腾讯',
        applicationLink: 'https://join.qq.com',
        industry: '互联网',
        tags: '技术岗',
        batch: '秋招',
        isHot: '',
        position: '后端开发工程师',
        location: '深圳',
        deadline: '2024-10-31'
      },
      {
        id: 2,
        updateTime: '2024-08-01',
        company: '阿里巴巴',
        applicationLink: 'https://talent.taobao.com',
        industry: '互联网',
        tags: '技术岗',
        batch: '秋招',
        isHot: '',
        position: '前端开发工程师',
        location: '杭州',
        deadline: '2024-11-15'
      },
      {
        id: 3,
        updateTime: '2024-08-01',
        company: '字节跳动',
        applicationLink: 'https://jobs.bytedance.com',
        industry: '互联网',
        tags: '技术岗',
        batch: '秋招',
        isHot: '',
        position: '算法工程师',
        location: '北京',
        deadline: '2024-11-30'
      },
      {
        id: 4,
        updateTime: '2024-08-01',
        company: '美团',
        applicationLink: 'https://zhaopin.meituan.com',
        industry: '互联网',
        tags: '产品岗',
        batch: '秋招',
        isHot: '',
        position: '产品经理',
        location: '北京',
        deadline: '2024-12-15'
      },
      // 添加更多测试数据以验证滚动功能
      ...Array.from({ length: 30 }, (_, index) => ({
        id: 5 + index,
        updateTime: `2024-08-${String(Math.floor(index / 10) + 1).padStart(2, '0')}`,
        company: `测试公司${index + 1}`,
        applicationLink: `https://test${index + 1}.com/careers`,
        industry: ['互联网', '金融', '制造业', '教育', '医疗', '房地产', '零售'][index % 7],
        tags: ['技术岗', '产品岗', '运营岗', '市场岗', '设计岗'][index % 5],
        batch: ['秋招', '春招', '实习'][index % 3],
        isHot: ['', '1', '2', '3'][index % 4],
        position: `测试职位${index + 1}`,
        location: ['北京', '上海', '深圳', '杭州', '广州', '成都', '武汉'][index % 7],
        deadline: `2024-${String(9 + Math.floor(index / 15)).padStart(2, '0')}-${String(15 + (index % 15)).padStart(2, '0')}`
      }))
    ]
    
    recruitments.value = defaultData
    saveToStorage()
  }

  // 数据验证
  const validateRecruitment = (recruitment: RecruitmentSummary): boolean => {
    return !!(
      recruitment.company &&
      recruitment.position &&
      recruitment.industry &&
      recruitment.batch
    )
  }

  // 本地存储
  const saveToStorage = () => {
    try {
      localStorage.setItem('recruitment-data', JSON.stringify(recruitments.value))
      console.log('校招数据已保存，共', recruitments.value.length, '条记录')
    } catch (error) {
      console.error('保存校招数据失败:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('recruitment-data')
      if (stored) {
        const data = JSON.parse(stored)
        if (Array.isArray(data) && data.length > 0) {
          // 转换旧数据格式，确保isHot是字符串类型
          const convertedData = data.map(item => ({
            ...item,
            isHot: typeof item.isHot === 'boolean'
              ? (item.isHot ? '热门' : '')
              : String(item.isHot || ''),
            updateTime: item.updateTime || ''
          }))
          recruitments.value = convertedData
          console.log('从本地存储加载校招数据，共', convertedData.length, '条记录')
          return
        }
      }
    } catch (error) {
      console.error('加载校招数据失败:', error)
    }

    // 如果没有存储数据或加载失败，初始化默认数据
    initializeDefaultData()
  }

  // 添加新记录
  const addRecruitment = (recruitment: Omit<RecruitmentSummary, 'id'>) => {
    const newId = Math.max(...recruitments.value.map(item => item.id), 0) + 1
    
    const newRecruitment: RecruitmentSummary = {
      ...recruitment,
      id: newId
    }

    // 验证新记录
    if (validateRecruitment(newRecruitment)) {
      recruitments.value.unshift(newRecruitment)
      saveToStorage()
      console.log(`添加新校招记录: ${newRecruitment.company} - ${newRecruitment.position}`)
      return newRecruitment
    } else {
      console.error('新校招记录数据格式不正确')
      return null
    }
  }

  // 更新记录
  const updateRecruitment = (id: number, updates: Partial<RecruitmentSummary>) => {
    const index = recruitments.value.findIndex(item => item.id === id)
    
    if (index !== -1) {
      const originalItem = recruitments.value[index]
      const updatedItem = { ...originalItem, ...updates }

      // 验证更新后的记录
      if (validateRecruitment(updatedItem)) {
        recruitments.value[index] = updatedItem
        saveToStorage()
        console.log(`更新校招记录 ID:${id}`, Object.keys(updates))
        return recruitments.value[index]
      } else {
        console.error('更新后的校招记录数据格式不正确')
        return null
      }
    }
    console.warn(`未找到 ID 为 ${id} 的校招记录`)
    return null
  }

  // 删除记录
  const deleteRecruitment = (id: number) => {
    const index = recruitments.value.findIndex(item => item.id === id)
    if (index !== -1) {
      const deleted = recruitments.value.splice(index, 1)[0]
      saveToStorage()
      console.log(`删除校招记录: ${deleted.company} - ${deleted.position}`)
      return deleted
    }
    console.warn(`未找到 ID 为 ${id} 的校招记录`)
    return null
  }

  // 批量删除
  const deleteMultipleRecruitments = (ids: number[]) => {
    const deletedItems: RecruitmentSummary[] = []
    
    // 按ID降序排序，从后往前删除，避免索引变化问题
    const sortedIds = [...ids].sort((a, b) => b - a)
    
    sortedIds.forEach(id => {
      const index = recruitments.value.findIndex(item => item.id === id)
      if (index !== -1) {
        const deleted = recruitments.value.splice(index, 1)[0]
        deletedItems.push(deleted)
      }
    })
    
    if (deletedItems.length > 0) {
      saveToStorage()
      console.log(`批量删除 ${deletedItems.length} 条校招记录`)
    }
    
    return deletedItems
  }

  // 重置为默认数据
  const resetToDefault = () => {
    initializeDefaultData()
    console.log('校招数据已重置为默认数据')
  }

  return {
    // 状态
    recruitments,
    loading,

    // 方法
    loadFromStorage,
    saveToStorage,
    addRecruitment,
    updateRecruitment,
    deleteRecruitment,
    deleteMultipleRecruitments,
    resetToDefault,
    validateRecruitment
  }
})
