cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\03_study\vs-re - 副本\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=assistant
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_zhipin
cargo:rustc-check-cfg=cfg(dev)
cargo:PERMISSION_FILES_PATH=D:\03_study\vs-re - 副本\src-tauri\target\release\build\app-a37f8de607671cfd\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
TOML parsing error
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\03_study\vs-re - 副本\src-tauri\target\release\build\app-a37f8de607671cfd\out\resource.lib
cargo:rustc-link-search=native=D:\03_study\vs-re - 副本\src-tauri\target\release\build\app-a37f8de607671cfd\out
cargo:rustc-link-arg=/NODEFAULTLIB:libvcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntime.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libcmtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrtd.lib
cargo:rustc-link-arg=/DEFAULTLIB:libcmt.lib
cargo:rustc-link-arg=/DEFAULTLIB:libvcruntime.lib
cargo:rustc-link-arg=/DEFAULTLIB:ucrt.lib
