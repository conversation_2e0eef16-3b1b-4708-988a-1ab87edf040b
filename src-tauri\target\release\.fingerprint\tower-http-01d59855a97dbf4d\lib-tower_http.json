{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2040997289075261528, "path": 7852854984642958252, "deps": [[784494742817713399, "tower_service", false, 9308697350011711417], [1906322745568073236, "pin_project_lite", false, 9610856809156372283], [4121350475192885151, "iri_string", false, 17811282871184522516], [5695049318159433696, "tower", false, 9778525692233992719], [7712452662827335977, "tower_layer", false, 7931180509596524673], [7896293946984509699, "bitflags", false, 287049618089675075], [9010263965687315507, "http", false, 10156329732527234393], [10629569228670356391, "futures_util", false, 4267093478471002299], [14084095096285906100, "http_body", false, 8313975913982668566], [16066129441945555748, "bytes", false, 7230401905503601804]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-01d59855a97dbf4d\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}