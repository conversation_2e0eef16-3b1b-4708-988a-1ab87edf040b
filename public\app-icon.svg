<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bgGradient)"/>
  
  <!-- 阴影效果 -->
  <rect x="4" y="4" width="248" height="248" rx="44" ry="44" fill="rgba(0,0,0,0.1)"/>
  <rect width="256" height="256" rx="48" ry="48" fill="url(#bgGradient)"/>
  
  <!-- 主图标 - 简历文档 -->
  <g transform="translate(64, 48)">
    <!-- 文档背景 -->
    <rect x="0" y="0" width="96" height="128" rx="8" ry="8" fill="url(#iconGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    
    <!-- 文档折角 -->
    <path d="M 72 0 L 96 0 L 96 24 Z" fill="rgba(255,255,255,0.8)"/>
    <path d="M 72 0 L 72 24 L 96 24" fill="none" stroke="rgba(79,70,229,0.3)" stroke-width="1"/>
    
    <!-- 文档内容线条 -->
    <rect x="12" y="40" width="60" height="3" rx="1.5" fill="rgba(79,70,229,0.6)"/>
    <rect x="12" y="52" width="72" height="2" rx="1" fill="rgba(79,70,229,0.4)"/>
    <rect x="12" y="60" width="48" height="2" rx="1" fill="rgba(79,70,229,0.4)"/>
    <rect x="12" y="68" width="66" height="2" rx="1" fill="rgba(79,70,229,0.4)"/>
    
    <!-- 分隔线 -->
    <rect x="12" y="84" width="72" height="1" fill="rgba(79,70,229,0.2)"/>
    
    <!-- 更多内容线条 -->
    <rect x="12" y="96" width="54" height="2" rx="1" fill="rgba(79,70,229,0.4)"/>
    <rect x="12" y="104" width="42" height="2" rx="1" fill="rgba(79,70,229,0.4)"/>
    <rect x="12" y="112" width="60" height="2" rx="1" fill="rgba(79,70,229,0.4)"/>
  </g>
  
  <!-- 装饰性元素 - 搜索图标 -->
  <g transform="translate(180, 180)">
    <circle cx="12" cy="12" r="8" fill="none" stroke="rgba(255,255,255,0.8)" stroke-width="3"/>
    <path d="m18 18 4 4" stroke="rgba(255,255,255,0.8)" stroke-width="3" stroke-linecap="round"/>
  </g>
  
  <!-- 装饰性元素 - 星星 -->
  <g transform="translate(48, 200)">
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" 
          fill="rgba(255,255,255,0.6)" transform="scale(0.8)"/>
  </g>
</svg>
