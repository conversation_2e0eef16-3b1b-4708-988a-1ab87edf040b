{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 6964799555348265985, "deps": [[40386456601120721, "percent_encoding", false, 2557365499804753774], [654232091421095663, "tauri_utils", false, 12011921763369382356], [1200537532907108615, "url<PERSON><PERSON>n", false, 11801558257907785920], [1967864351173319501, "muda", false, 16930182267985590054], [2013030631243296465, "webview2_com", false, 8179563293025496140], [3150220818285335163, "url", false, 3140987579113747913], [3331586631144870129, "getrandom", false, 4822259233550213675], [4143744114649553716, "raw_window_handle", false, 9730845076325814965], [4919829919303820331, "serialize_to_javascript", false, 17809035334422530848], [5986029879202738730, "log", false, 14425135604263769896], [9010263965687315507, "http", false, 10156329732527234393], [9689903380558560274, "serde", false, 4567830511950114312], [10229185211513642314, "mime", false, 12583440156617515302], [10806645703491011684, "thiserror", false, 798670949607384445], [11989259058781683633, "dunce", false, 7882815010359764019], [12092653563678505622, "build_script_build", false, 17052202899347285268], [12304025191202589669, "tauri_runtime_wry", false, 11518775776782799134], [12565293087094287914, "window_vibrancy", false, 12189064357102582387], [12943761728066819757, "tauri_runtime", false, 12546026217587187799], [12986574360607194341, "serde_repr", false, 2996903480416956038], [13077543566650298139, "heck", false, 12719665928774161065], [13405681745520956630, "tauri_macros", false, 14409757911850727881], [13625485746686963219, "anyhow", false, 13014709620378321028], [14585479307175734061, "windows", false, 2741241964980341311], [16362055519698394275, "serde_json", false, 1560433525378758638], [16928111194414003569, "dirs", false, 7473585614917981949], [17155886227862585100, "glob", false, 480147955432837878], [17531218394775549125, "tokio", false, 13860976087988092850]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-6c0104cb059c43fc\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}